// 测试Element Plus图标是否存在的工具函数
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 获取所有可用的图标名称
export const getAllIconNames = () => {
  return Object.keys(ElementPlusIconsVue)
}

// 检查特定图标是否存在
export const checkIconExists = (iconName) => {
  return iconName in ElementPlusIconsVue
}

// 搜索包含特定关键词的图标
export const searchIcons = (keyword) => {
  const allIcons = Object.keys(ElementPlusIconsVue)
  return allIcons.filter(icon => 
    icon.toLowerCase().includes(keyword.toLowerCase())
  )
}

// 在控制台打印所有图标（用于调试）
export const logAllIcons = () => {
  const icons = getAllIconNames()
  console.log('所有可用的Element Plus图标:', icons)
  console.log('图标总数:', icons.length)
  return icons
}

// 检查我们项目中使用的图标是否都存在
export const checkProjectIcons = () => {
  const projectIcons = [
    'QuestionFilled',
    'Bell',
    'User',
    'Setting',
    'SwitchButton',
    'ArrowDown',
    'House',
    'DataAnalysis',
    'Brush',
    'EditPen',
    'Collection',
    'School',
    'Cpu',
    'List',
    'Edit',
    'Reading',
    'Folder',
    'VideoPlay',
    'Trophy',
    'Clock',
    'Guide',
    'DArrowRight',
    'DArrowLeft',
    'ArrowRight'
  ]
  
  const results = {}
  projectIcons.forEach(icon => {
    results[icon] = checkIconExists(icon)
  })
  
  console.log('项目图标检查结果:', results)
  return results
}
