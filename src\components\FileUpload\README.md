# FileUpload 原生文件上传组件

一个基于原生 HTML input 的强壮完善的文件上传组件，支持图片预览、拖拽上传、进度显示等功能。

## 功能特性

- ✅ **原生 Input** - 基于原生 HTML input，兼容性更好
- ✅ **文件验证** - 支持格式、大小、数量限制
- ✅ **拖拽上传** - 支持文件拖拽到上传区域
- ✅ **图片预览** - 内置图片预览和模态框查看
- ✅ **上传进度** - 实时显示上传进度
- ✅ **文件管理** - 支持删除、重新上传等操作
- ✅ **自定义样式** - 完全可定制的界面
- ✅ **事件丰富** - 完整的事件回调系统
- ✅ **TypeScript** - 完整的类型支持
- ✅ **无依赖** - 不依赖第三方上传库

## 基础用法

```vue
<template>
  <FileUpload 
    v-model="fileList"
    :upload-function="uploadFile"
  />
</template>

<script setup>
import { ref } from 'vue'
import FileUpload from '@/components/FileUpload/Index.vue'

const fileList = ref([])

// 上传函数
const uploadFile = async (file, onProgress) => {
  const formData = new FormData()
  formData.append('file', file)
  
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    
    // 监听上传进度
    xhr.upload.onprogress = (e) => {
      if (e.lengthComputable) {
        const progress = Math.round((e.loaded / e.total) * 100)
        onProgress(progress)
      }
    }
    
    xhr.onload = () => {
      if (xhr.status === 200) {
        const response = JSON.parse(xhr.responseText)
        resolve(response)
      } else {
        reject(new Error('Upload failed'))
      }
    }
    
    xhr.onerror = () => reject(new Error('Upload error'))
    
    xhr.open('POST', '/api/upload')
    xhr.send(formData)
  })
}
</script>
```

## 高级用法

### 多文件上传
```vue
<template>
  <FileUpload 
    v-model="fileList"
    :multiple="true"
    :max-count="5"
    :upload-function="uploadFile"
    trigger-text="选择多个文件"
  />
</template>
```

### 拖拽上传
```vue
<template>
  <FileUpload 
    v-model="fileList"
    :support-drag="true"
    :upload-function="uploadFile"
    trigger-text="点击或拖拽文件到此处"
    hint-text="支持拖拽上传"
  />
</template>
```

### 自定义配置
```vue
<template>
  <FileUpload 
    v-model="fileList"
    accept="image/*"
    :max-size="10"
    :allowed-formats="['jpg', 'png', 'gif']"
    :auto-upload="false"
    :upload-function="uploadFile"
    @change="handleChange"
    @upload-success="handleSuccess"
    @upload-error="handleError"
  />
</template>
```

### 自定义触发器
```vue
<template>
  <FileUpload v-model="fileList" :upload-function="uploadFile">
    <template #trigger>
      <div class="custom-trigger">
        <el-button type="primary">
          <el-icon><Upload /></el-icon>
          选择文件
        </el-button>
      </div>
    </template>
    
    <template #tips>
      <div class="custom-tips">
        <p>• 支持 JPG、PNG、GIF 格式</p>
        <p>• 单个文件不超过 5MB</p>
        <p>• 建议图片尺寸：800x600</p>
      </div>
    </template>
  </FileUpload>
</template>
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | Array | [] | 文件列表，支持 v-model |
| accept | String | 'image/*' | 接受的文件类型 |
| multiple | Boolean | false | 是否支持多选 |
| maxCount | Number | 1 | 最大文件数量 |
| maxSize | Number | 5 | 文件大小限制(MB) |
| allowedFormats | Array | ['jpg', 'jpeg', 'png', 'gif', 'webp'] | 允许的文件格式 |
| disabled | Boolean | false | 是否禁用 |
| showFileList | Boolean | true | 是否显示文件列表 |
| supportDrag | Boolean | true | 是否支持拖拽 |
| triggerText | String | '点击上传' | 触发按钮文本 |
| hintText | String | '或将文件拖拽到此处' | 提示文本 |
| showHint | Boolean | true | 是否显示提示 |
| showTips | Boolean | true | 是否显示底部提示 |
| tipText | String | '' | 底部提示文本 |
| autoUpload | Boolean | true | 是否自动上传 |
| uploadFunction | Function | null | 上传函数 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| change | (files, fileList) | 文件列表变化时触发 |
| remove | (file, fileList) | 删除文件时触发 |
| preview | (file) | 预览文件时触发 |
| upload-success | (response, file) | 上传成功时触发 |
| upload-error | (error, file) | 上传失败时触发 |
| upload-progress | (file, progress) | 上传进度变化时触发 |
| exceed | (files, fileList) | 超出文件数量限制时触发 |

## Methods 方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| submit | - | 手动上传所有文件 |
| clearFiles | - | 清空文件列表 |
| triggerFileInput | - | 触发文件选择 |

## Slots 插槽

| 插槽名 | 说明 |
|--------|------|
| trigger | 自定义触发器内容 |
| tips | 自定义底部提示内容 |

## 上传函数格式

上传函数需要返回 Promise，并支持进度回调：

```javascript
const uploadFunction = async (file, onProgress) => {
  // file: 原始文件对象
  // onProgress: 进度回调函数，参数为 0-100 的数字
  
  // 实现上传逻辑
  // 调用 onProgress(progress) 更新进度
  // 返回包含 url 字段的对象或直接返回 url 字符串
  
  return { url: 'https://example.com/uploaded-file.jpg' }
}
```

## 文件对象结构

```javascript
{
  uid: 'unique-id',        // 唯一标识
  name: 'filename.jpg',    // 文件名
  size: 1024000,          // 文件大小(字节)
  type: 'image/jpeg',     // 文件类型
  raw: File,              // 原始文件对象
  status: 'success',      // 状态: ready/uploading/success/error
  progress: 100,          // 上传进度 0-100
  preview: 'data:image...', // 预览图片(base64)
  url: 'https://...'      // 上传后的URL
}
```

## 样式定制

组件使用 SCSS 编写，支持通过 CSS 变量定制：

```scss
.file-upload-component {
  // 自定义上传区域样式
  .upload-area {
    border-color: #your-color;
    background: #your-bg;
    
    &:hover {
      border-color: #your-hover-color;
    }
  }
  
  // 自定义文件列表样式
  .file-list .file-item {
    border-color: #your-border;
    background: #your-bg;
  }
}
```

## 注意事项

1. **上传函数必须返回 Promise**
2. **进度回调参数范围是 0-100**
3. **文件验证在客户端进行，服务端也需要验证**
4. **拖拽功能需要现代浏览器支持**
5. **大文件上传建议分片处理**
6. **组件不处理文件存储，需要配合后端接口**

## 与 Element Plus Upload 对比

| 特性 | FileUpload | Element Plus Upload |
|------|------------|-------------------|
| 基础实现 | 原生 input | XMLHttpRequest |
| 包大小 | 更小 | 较大 |
| 自定义程度 | 完全可控 | 受限于组件设计 |
| 兼容性 | 更好 | 依赖现代浏览器 |
| 功能丰富度 | 基础但完整 | 功能更丰富 |
| 学习成本 | 较低 | 需要了解组件API |
