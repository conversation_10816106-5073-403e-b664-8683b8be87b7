<template>
  <div class="image-upload">
    <el-upload ref="uploadRef" :action="uploadUrl" :headers="uploadHeaders" :data="uploadData" :file-list="fileList"
      :before-upload="beforeUpload" :on-success="handleSuccess" :on-error="handleError" :on-remove="handleRemove"
      :on-preview="handlePreview" :on-exceed="handleExceed" :limit="limit" :accept="accept" :disabled="disabled"
      :show-file-list="showFileList" :drag="drag" :multiple="multiple" list-type="picture-card"
      class="upload-component">
      <div class="upload-trigger" v-if="!hideUploadButton">
        <slot name="trigger">
          <div class="default-trigger">
            <el-icon class="upload-icon">
              <Plus />
            </el-icon>
            <div class="upload-text" v-if="!drag">{{ triggerText }}</div>
          </div>
        </slot>
      </div>

      <template #tip v-if="showTip">
        <div class="upload-tip">
          <slot name="tip">
            <span>{{ tipText }}</span>
          </slot>
        </div>
      </template>
    </el-upload>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" :title="previewTitle" width="800px" append-to-body class="preview-dialog">
      <img :src="previewUrl" alt="预览图片" class="preview-image" />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// Props 定义
const props = defineProps({
  // 上传地址
  action: {
    type: String,
    default: '/api/upload'
  },
  // 请求头
  headers: {
    type: Object,
    default: () => ({})
  },
  // 上传时附带的额外参数
  data: {
    type: Object,
    default: () => ({})
  },
  // 文件列表
  modelValue: {
    type: Array,
    default: () => []
  },
  // 最大上传数量
  limit: {
    type: Number,
    default: 1
  },
  // 文件大小限制 (MB)
  maxSize: {
    type: Number,
    default: 20
  },
  // 接受的文件类型
  accept: {
    type: String,
    default: 'image/*'
  },
  // 允许的文件格式
  allowedFormats: {
    type: Array,
    default: () => ['jpg', 'jpeg', 'png', 'gif', 'webp']
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否显示文件列表
  showFileList: {
    type: Boolean,
    default: true
  },
  // 是否支持拖拽上传
  drag: {
    type: Boolean,
    default: false
  },
  // 是否支持多选
  multiple: {
    type: Boolean,
    default: false
  },
  // 触发按钮文本
  triggerText: {
    type: String,
    default: '上传图片'
  },
  // 提示文本
  tipText: {
    type: String,
    default: ''
  },
  // 是否显示提示
  showTip: {
    type: Boolean,
    default: true
  },
  // 是否自动上传
  autoUpload: {
    type: Boolean,
    default: true
  },
  // 图片压缩质量 (0-1)
  compressQuality: {
    type: Number,
    default: 0.8
  },
  // 是否启用图片压缩
  enableCompress: {
    type: Boolean,
    default: true
  }
})

// Emits 定义
const emit = defineEmits([
  'update:modelValue',
  'success',
  'error',
  'remove',
  'preview',
  'exceed',
  'before-upload'
])

// 响应式数据
const uploadRef = ref()
const fileList = ref([])
const previewVisible = ref(false)
const previewUrl = ref('')
const previewTitle = ref('图片预览')

// 计算属性
const uploadUrl = computed(() => props.action)
const uploadHeaders = computed(() => props.headers)
const uploadData = computed(() => props.data)

const hideUploadButton = computed(() => {
  return props.limit <= fileList.value.length
})

const computedTipText = computed(() => {
  if (props.tipText) return props.tipText

  const formats = props.allowedFormats.join('/')
  const size = props.maxSize
  const limit = props.limit

  return `支持 ${formats} 格式，单个文件不超过 ${size}MB${limit > 1 ? `，最多上传 ${limit} 个文件` : ''}`
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  fileList.value = newVal || []
}, { immediate: true, deep: true })

// 上传前的钩子
const beforeUpload = (file) => {
  // 文件格式验证
  const fileExtension = file.name.split('.').pop().toLowerCase()
  if (!props.allowedFormats.includes(fileExtension)) {
    ElMessage.error(`不支持的文件格式，请上传 ${props.allowedFormats.join('/')} 格式的图片`)
    return false
  }

  // 文件大小验证
  const isLtMaxSize = file.size / 1024 / 1024 < props.maxSize
  if (!isLtMaxSize) {
    ElMessage.error(`图片大小不能超过 ${props.maxSize}MB`)
    return false
  }

  // 图片尺寸验证（可选）
  return new Promise((resolve) => {
    const img = new Image()
    img.onload = () => {
      // 可以在这里添加图片尺寸验证
      // if (img.width > 1920 || img.height > 1080) {
      //   ElMessage.error('图片尺寸不能超过 1920x1080')
      //   resolve(false)
      //   return
      // }

      // 图片压缩（如果启用）
      if (props.enableCompress) {
        compressImage(file, props.compressQuality).then(resolve)
      } else {
        resolve(true)
      }
    }
    img.onerror = () => {
      ElMessage.error('无效的图片文件')
      resolve(false)
    }
    img.src = URL.createObjectURL(file)
  })
}

// 图片压缩函数
const compressImage = (file, quality = 0.8) => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      canvas.width = img.width
      canvas.height = img.height
      ctx.drawImage(img, 0, 0)

      canvas.toBlob((blob) => {
        if (blob) {
          const compressedFile = new File([blob], file.name, {
            type: file.type,
            lastModified: Date.now()
          })
          resolve(compressedFile)
        } else {
          resolve(file)
        }
      }, file.type, quality)
    }

    img.src = URL.createObjectURL(file)
  })
}

// 上传成功回调
const handleSuccess = (response, file, fileList) => {
  const newFileList = fileList.map(item => ({
    name: item.name,
    url: item.response?.url || item.url,
    uid: item.uid,
    status: item.status
  }))

  emit('update:modelValue', newFileList)
  emit('success', response, file, fileList)
  ElMessage.success('上传成功')
}

// 上传失败回调
const handleError = (error, file, fileList) => {
  emit('error', error, file, fileList)
  ElMessage.error('上传失败，请重试')
}

// 删除文件回调
const handleRemove = (file, fileList) => {
  ElMessageBox.confirm('确定要删除这张图片吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const newFileList = fileList.map(item => ({
      name: item.name,
      url: item.response?.url || item.url,
      uid: item.uid,
      status: item.status
    }))

    emit('update:modelValue', newFileList)
    emit('remove', file, fileList)
    ElMessage.success('删除成功')
  }).catch(() => {
    // 取消删除，恢复文件列表
    return false
  })
}

// 预览图片
const handlePreview = (file) => {
  previewUrl.value = file.url || file.response?.url
  previewTitle.value = file.name
  previewVisible.value = true
  emit('preview', file)
}

// 超出文件数量限制
const handleExceed = (files, fileList) => {
  ElMessage.warning(`最多只能上传 ${props.limit} 个文件`)
  emit('exceed', files, fileList)
}

// 手动上传
const submit = () => {
  uploadRef.value?.submit()
}

// 清空文件列表
const clearFiles = () => {
  uploadRef.value?.clearFiles()
  emit('update:modelValue', [])
}

// 暴露方法
defineExpose({
  submit,
  clearFiles
})
</script>

<style lang="scss" scoped>
.image-upload {
  .upload-component {
    :deep(.el-upload--picture-card) {
      width: 100%;
      height: 100%;
      border-radius: 8px;
      border: 2px dashed transparent;
      transition: all 0.3s ease;
    }

    :deep(.el-upload-list--picture-card) {
      .el-upload-list__item {
        width: 100%;
        height: 100%;
      }
    }
  }

  .upload-trigger {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .default-trigger {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #8c939d;

      .upload-icon {
        font-size: 28px;
        margin-bottom: 8px;
      }

      .upload-text {
        font-size: 14px;
      }
    }
  }

  .upload-tip {
    margin-top: 8px;
    color: #606266;
    font-size: 12px;
    line-height: 1.4;
  }
}

.preview-dialog {
  .preview-image {
    width: 100%;
    max-height: 600px;
    object-fit: contain;
  }
}
</style>
