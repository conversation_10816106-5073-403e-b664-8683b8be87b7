# 认证系统设置文档

## 概述

本项目已集成了完整的认证系统，包括：
- Pinia状态管理
- Axios HTTP客户端和拦截器
- JWT Token管理
- 登录/登出功能
- 路由守卫
- 自动token刷新

## 已安装的依赖

```bash
npm install pinia axios
```

## 文件结构

```
src/
├── stores/
│   ├── index.js          # Pinia配置
│   └── auth.js           # 认证状态管理
├── utils/
│   └── request.js        # Axios配置和拦截器
├── api/
│   └── auth.js           # 认证相关API
├── views/
│   └── Login.vue         # 登录页面
└── router/
    └── index.js          # 路由配置（已更新）
```

## 功能特性

### 1. Token管理
- 自动在localStorage中持久化token
- 请求时自动添加Authorization头
- Token过期自动刷新
- 刷新失败自动跳转登录页

### 2. 路由守卫
- 自动检查路由权限
- 未登录用户重定向到登录页
- 已登录用户访问登录页重定向到首页
- 保存重定向路径，登录后自动跳转

### 3. 请求拦截器
- 自动添加token到请求头
- 统一错误处理
- 请求重试机制
- 加载状态管理

### 4. 响应拦截器
- 统一响应格式处理
- 401/403错误自动处理
- 网络错误友好提示
- 表单验证错误处理

## 使用方法

### 1. 登录
访问任何需要认证的页面会自动跳转到登录页面：
```
http://localhost:5174/login
```

测试账号（Mock模式）：
- 用户名：任意
- 密码：任意（至少6位）

### 2. API调用示例

```javascript
import { get, post } from '@/utils/request'

// GET请求
const data = await get('/api/users')

// POST请求
const result = await post('/api/users', { name: 'John' })
```

### 3. 在组件中使用认证状态

```vue
<script setup>
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// 检查登录状态
console.log(authStore.isAuthenticated)

// 获取用户信息
console.log(authStore.userInfo)

// 登出
const logout = () => {
  authStore.logout()
}
</script>
```

## 配置

### 环境变量 (.env)
```env
# API基础URL
VITE_API_BASE_URL=http://localhost:3000/api

# 是否启用Mock数据
VITE_USE_MOCK=true
```

### 切换到真实API
1. 设置 `VITE_USE_MOCK=false`
2. 配置正确的 `VITE_API_BASE_URL`
3. 确保后端API返回正确的数据格式

## API数据格式

### 登录响应格式
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "jwt-token-string",
    "userInfo": {
      "id": 1,
      "username": "user",
      "email": "<EMAIL>",
      "avatar": "",
      "roles": ["user"]
    }
  }
}
```

### 错误响应格式
```json
{
  "code": 401,
  "message": "登录已过期",
  "data": null
}
```

## 安全特性

1. **Token自动刷新**：Token即将过期时自动刷新
2. **请求重试**：网络错误时自动重试
3. **路由保护**：未授权访问自动重定向
4. **状态持久化**：刷新页面保持登录状态
5. **错误处理**：统一的错误提示和处理

## 开发建议

1. 在生产环境中设置正确的API地址
2. 实现真实的后端认证API
3. 根据需要调整token过期时间
4. 添加更多的用户权限检查
5. 实现记住登录功能

## 故障排除

### 常见问题

1. **登录后立即跳转到登录页**
   - 检查token格式是否正确
   - 确认后端返回的数据格式

2. **请求401错误**
   - 检查token是否正确设置
   - 确认后端token验证逻辑

3. **路由守卫不生效**
   - 检查路由meta配置
   - 确认Pinia store正确初始化

4. **刷新页面丢失登录状态**
   - 检查localStorage存储
   - 确认initAuth方法调用
