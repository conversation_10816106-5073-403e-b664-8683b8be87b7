<template>
  <div class="login-container">
    <div class="brand-logo">
      <img class="logo" src="@/assets/login-logo.png">
    </div>
    <div class="main-content">
      <div class="register-section" v-if="currentStep === 2">
        <div class="select-identity">
          <div class="identity-item s-education-provider" @click="selectIdentity(1)"
            v-if="registerForm.user_type === 1">
            <span class="text">EDUCATION PROVIDER</span>
          </div>
          <div class="identity-item n-education-provider" @click="selectIdentity(1)"
            v-if="[0, 2].includes(registerForm.user_type)">
            <span class="text">EDUCATION PROVIDER</span>
          </div>
          <div class="identity-item s-student" @click="selectIdentity(2)" v-if="registerForm.user_type === 2">
            <span class="text">STUDENT</span>
          </div>
          <div class="identity-item n-student" @click="selectIdentity(2)"
            v-if="[0, 1].includes(registerForm.user_type)">
            <span class="text">STUDENT</span>
          </div>
        </div>
        <el-button type="primary" size="large" class="select-button" @click="handleNext">
          Next
        </el-button>
        <p class="login-link" @click="changeToLogin">Already have an account, go to login ></p>
      </div>
      <div class="register-section-two" v-if="currentStep === 3">
        <div class="register-header">
          <h2>Education Provider Registration</h2>
        </div>

        <el-form ref="registerFormRef" :model="registerForm" :rules="registerRules" class="register-form">
          <!-- 基本信息 -->
          <div class="form-section">
            <div class="section-title">
              <img class="icon" src="@/assets/basic-information.png" alt="icon">
              <span>Basic information</span>
            </div>

            <div class="form-row">
              <div class="avatar-upload">
                <el-form-item prop="avatar" class="avatar-form-item">
                  <FileUpload @uploadData="handleAvatarSuccess">
                    <template #trigger>
                      <div class="avatar-placeholder">
                        <img v-if="avatarPreview" :src="avatarPreview" alt="avatar" class="avatar-preview" />
                        <img v-else class="icon" src="@/assets/avatar-add.png" alt="icon">
                      </div>
                    </template>
                  </FileUpload>
                </el-form-item>
                <span class="avatar-label">Avatar</span>
              </div>

              <div class="form-inputs">
                <div class="input-row">
                  <el-form-item prop="username">
                    <el-input v-model="registerForm.username" placeholder="Username" size="large" />
                  </el-form-item>
                  <el-form-item prop="password">
                    <el-input v-model="registerForm.password" type="password" placeholder="Password" size="large"
                      show-password />
                  </el-form-item>
                </div>
                <div class="input-row">
                  <el-form-item prop="email">
                    <el-input v-model="registerForm.email" placeholder="Email address" size="large" />
                  </el-form-item>
                  <el-form-item prop="confirm_password">
                    <el-input v-model="registerForm.confirm_password" type="password" placeholder="Confirm password"
                      size="large" show-password />
                  </el-form-item>
                </div>
              </div>
            </div>
          </div>

          <!-- 教育背景和专业资格 -->
          <div class="form-section">
            <div class="section-title">
              <img class="icon" src="@/assets/educational-icon.png" alt="icon">

              <span>Educational background and professional qualification</span>
            </div>

            <div class="form-row">
              <div class="form-left">
                <el-form-item prop="education">
                  <el-select v-model="registerForm.education" placeholder="Highest education level" size="large">
                    <el-option v-for="option in educationLevelOptions" :key="option.value" :label="option.label"
                      :value="option.value" />
                  </el-select>
                </el-form-item>

                <el-form-item prop="school">
                  <el-input v-model="registerForm.school" placeholder="Name of educational institution" size="large" />
                </el-form-item>

                <el-form-item prop="profession">
                  <el-select v-model="registerForm.profession" placeholder="Professional field" size="large">
                    <el-option v-for="option in professionalFieldOptions" :key="option.value" :label="option.label"
                      :value="option.value" />
                  </el-select>
                </el-form-item>

                <el-form-item prop="teaching_experience">
                  <el-select v-model="registerForm.teaching_experience" placeholder="Years of teaching experience"
                    size="large">
                    <el-option v-for="option in teachingExperienceOptions" :key="option.value" :label="option.label"
                      :value="option.value" />
                  </el-select>
                </el-form-item>

                <el-form-item prop="teaching_method">
                  <el-select v-model="registerForm.teaching_method" placeholder="Preferred teaching method"
                    size="large">
                    <el-option v-for="option in teachingMethodOptions" :key="option.value" :label="option.label"
                      :value="option.value" />
                  </el-select>
                </el-form-item>
              </div>

              <div class="form-right">
                <div class="upload-section">
                  <div class="upload-area">
                    <img class="upload-file" src="@/assets/upload-files.png" alt="icon">
                    <span class="text">Upload relevant supporting documents</span>
                  </div>
                  <p class="upload-hint">Here are the names and size specifications of the files available for upload
                  </p>
                  <div class="verification-section">
                    <span>Online database verification: </span>
                    <el-link type="primary">LinkedIn</el-link>
                  </div>
                  <el-button type="primary" class="nft-button">Create NFT</el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 协议和通知 -->
          <div class="form-section">
            <el-form-item prop="agreement">
              <el-checkbox v-model="registerForm.agreement">
                <span class="agreement">Read agreement</span> <el-link type="primary">《Protocol Link》</el-link>
              </el-checkbox>
            </el-form-item>

            <el-form-item prop="notifications">
              <el-checkbox v-model="registerForm.notifications">
                <span>Receive course updates and news notifications</span>
              </el-checkbox>
            </el-form-item>
          </div>

          <!-- 提交按钮 -->
          <el-form-item>
            <el-button type="primary" size="large" class="submit-button" @click="handleRegister">
              SUBMIT
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="register-section-three" v-if="currentStep === 4">
        <div class="student-register-header">
          <h2>Student login</h2>
        </div>

        <el-form ref="studentFormRef" :model="studentForm" :rules="studentRules" class="student-form">
          <!-- 头像上传 -->
          <div class="avatar-section">
            <div class="avatar-upload-container">
              <FileUpload @uploadData="handleStudentAvatarSuccess">
                <template #trigger>
                  <div class="student-avatar-placeholder">
                    <img v-if="studentAvatarPreview" :src="studentAvatarPreview" alt="avatar"
                      class="student-avatar-preview" />
                    <img v-else class="icon" src="@/assets/avatar-add.png" alt="icon">
                  </div>
                </template>
              </FileUpload>
            </div>
            <span class="avatar-label">Avatar</span>
          </div>

          <!-- 表单输入项 -->
          <div class="form-inputs-section">
            <el-form-item prop="email">
              <el-input v-model="studentForm.email" placeholder="Email address" size="large" class="student-input">
                <template #suffix>
                  <img class="input-icon" src="@/assets/student-register-1.png" alt="icon">
                </template>
              </el-input>
            </el-form-item>

            <el-form-item prop="password">
              <el-input v-model="studentForm.password" type="password" placeholder="Password" size="large"
                class="student-input" show-password>
                <template #suffix>
                  <img class="input-icon" src="@/assets/student-register-2.png" alt="icon">
                </template>
              </el-input>
            </el-form-item>

            <el-form-item prop="birthDate">
              <div class="date-picker-input">
                <el-date-picker v-model="studentForm.birthDate" class="date-picker" type="date"
                  placeholder="Date of birth" size="large" format="YYYY-MM-DD" value-format="YYYY-MM-DD">
                </el-date-picker>
                <img class="input-icon" src="@/assets/student-register-3.png" alt="icon">
              </div>

            </el-form-item>
          </div>

          <!-- NFT创建按钮 -->
          <el-button type="primary" size="large" class="nft-creation-button">
            NFT Creation
          </el-button>

          <!-- 协议和通知复选框 -->
          <div class="checkbox-section">
            <el-checkbox v-model="studentForm.agreement">
              <span class="agreement-text">Read agreement</span>
              <el-link type="primary" class="protocol-link">《Protocol Link》</el-link>
            </el-checkbox>
            <el-checkbox v-model="studentForm.notifications">
              <span class="notification-text">Receive course updates and news notifications</span>
            </el-checkbox>
          </div>

          <!-- 提交按钮 -->
          <el-form-item>
            <el-button type="primary" size="large" class="student-submit-button" @click="handleStudentRegister">
              SUBMIT
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { h, ref, shallowRef, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import FileUpload from '@/components/FileUpload/Index.vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// 当前步骤：1-登录，2-选择注册类型，3-教育者注册，4-学习者注册
const currentStep = ref(4)


const registerFormRef = ref()
const studentFormRef = ref()

// 头像文件列表
const avatarList = ref([])
const avatarPreview = ref('')

// 学生头像
const studentAvatarPreview = ref('')

// 教育背景选项
const educationLevelOptions = [
  { label: "Bachelor's Degree", value: 'bachelor' },
  { label: "Master's Degree", value: 'master' },
  { label: "Doctoral Degree", value: 'doctoral' },
  { label: 'Other', value: 'other' }
]

// 专业领域选项
const professionalFieldOptions = [
  { label: 'Computer Science', value: 'cs' },
  { label: 'Mathematics', value: 'math' },
  { label: 'Physics', value: 'physics' },
  { label: 'Chemistry', value: 'chemistry' },
  { label: 'Biology', value: 'biology' },
  { label: 'Engineering', value: 'engineering' },
  { label: 'Business', value: 'business' },
  { label: 'Arts', value: 'arts' },
  { label: 'Other', value: 'other' }
]

// 教学经验选项
const teachingExperienceOptions = [
  { label: '0-1 years', value: '0-1' },
  { label: '2-5 years', value: '2-5' },
  { label: '6-10 years', value: '6-10' },
  { label: '10+ years', value: '10+' }
]

// 教学方法选项
const teachingMethodOptions = [
  { label: 'Online', value: 'online' },
  { label: 'Offline', value: 'offline' },
  { label: 'Hybrid', value: 'hybrid' }
]

// 注册表单数据
const registerForm = reactive({
  user_type: 0, // 1-教育者，2-学习者
  avatar: '', // 头像
  username: '', // 账号
  email: '', // 邮箱
  password: '', // 密码
  confirm_password: '', // 确认密码
  education: '', // 教育背景
  school: '', // 毕业院校
  profession: '', // 专业领域
  teaching_experience: '', // 教学经验
  teaching_method: '',
  agreement: false, // 注册协议
  notifications: false // 接收通知
})

// 学生表单数据
const studentForm = reactive({
  avatar: '', // 头像
  email: '', // 邮箱
  password: '', // 密码
  birthDate: '', // 出生日期
  agreement: false, // 注册协议
  notifications: false // 接收通知
})

// 注册表单验证规则
const registerRules = {
  username: [
    { required: true, message: 'Username is required', trigger: 'blur' },
    { min: 2, max: 50, message: 'Username length should be between 2 and 50 characters', trigger: 'blur' }
  ],
  avatar: [
    { required: true, message: 'Avatar is required', trigger: 'change' }
  ],
  email: [
    { required: true, message: 'Email is required', trigger: 'blur' },
    { type: 'email', message: 'Please enter a valid email address', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Password is required', trigger: 'blur' },
    { min: 6, max: 20, message: 'Password length should be between 6 and 20 characters', trigger: 'blur' }
  ],
  confirm_password: [
    { required: true, message: 'Please confirm your password', trigger: 'blur' },
    {
      validator: (_, value, callback) => {
        if (value !== registerForm.password) {
          callback(new Error('Passwords do not match'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  education: [
    { required: true, message: 'Please select your education level', trigger: 'change' }
  ],
  school: [
    { required: true, message: 'School name is required', trigger: 'blur' }
  ],
  profession: [
    { required: true, message: 'Please select your professional field', trigger: 'change' }
  ],
  teaching_experience: [
    { required: true, message: 'Please select your teaching experience', trigger: 'change' }
  ],
  teaching_method: [
    { required: true, message: 'Please select your preferred teaching method', trigger: 'change' }
  ],
  agreement: [
    {
      validator: (_, value, callback) => {
        if (!value) {
          callback(new Error('You must agree to the terms'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 学生表单验证规则
const studentRules = {
  avatar: [
    { required: true, message: 'Avatar is required', trigger: 'change' }
  ],
  email: [
    { required: true, message: 'Email is required', trigger: 'blur' },
    { type: 'email', message: 'Please enter a valid email address', trigger: 'blur' }
  ],
  password: [
    { required: true, message: 'Password is required', trigger: 'blur' },
    { min: 6, max: 20, message: 'Password length should be between 6 and 20 characters', trigger: 'blur' }
  ],
  birthDate: [
    { required: true, message: 'Date of birth is required', trigger: 'change' }
  ],
  agreement: [
    {
      validator: (_, value, callback) => {
        if (!value) {
          callback(new Error('You must agree to the terms'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 返回上一页
const changeToLogin = () => {
  router.back()
}

// 日期图标占位
const customPrefix = shallowRef({
  render() {
    return h('i', '')
  },
})

// 选择注册身份
const selectIdentity = (identity) => {
  registerForm.user_type = identity
}

// 选择身份下一步
const handleNext = () => {
  if (registerForm.user_type === 0) {
    ElMessage.warning('Please select an identity')
    return
  } else if (registerForm.user_type === 1) {
    currentStep.value = 3
  } else if (registerForm.user_type === 2) {
    currentStep.value = 4
  }
}

// 处理头像上传成功
const handleAvatarSuccess = (data) => {
  console.log('头像上传成功:', data)

  // 更新注册表单中的头像字段
  if (data && data.ossUrl) {
    avatarPreview.value = data.ossUrl
    registerForm.avatar = data.ossUrl

    // 手动触发表单验证，清除头像必填的错误提示
    registerFormRef.value?.validateField('avatar')
  }
}

// 处理学生头像上传成功
const handleStudentAvatarSuccess = (data) => {
  console.log('学生头像上传成功:', data)

  // 更新学生表单中的头像字段
  if (data && data.ossUrl) {
    studentAvatarPreview.value = data.ossUrl
    studentForm.avatar = data.ossUrl

    // 手动触发表单验证，清除头像必填的错误提示
    studentFormRef.value?.validateField('avatar')
  }
}

// 处理注册
const handleRegister = async () => {
  if (!registerFormRef.value) return

  try {
    // 验证表单
    const valid = await registerFormRef.value.validate()
    if (!valid) {
      return
    }

    // 判断头像
    if (!registerForm.avatar) {
      ElMessage.error('请上传头像')
      return
    }
    // 准备注册数据
    const registerData = {
      ...registerForm,
      avatar: avatarList.value.length > 0 ? avatarList.value[0].url : ''
    }

    // TODO: 实现实际的注册逻辑
    console.log('注册数据:', registerData)
    ElMessage.success('Registration submitted successfully')

    // 注册成功后可以跳转到登录页面或其他页面
    // currentStep.value = 1 // 跳转到登录页面

  } catch (error) {
    console.error('Registration error:', error)
    ElMessage.error('Registration failed, please check the registration information')
  }
}

// 处理学生注册
const handleStudentRegister = async () => {
  if (!studentFormRef.value) return

  try {
    // 验证表单
    const valid = await studentFormRef.value.validate()
    if (!valid) {
      return
    }

    // 判断头像
    if (!studentForm.avatar) {
      ElMessage.error('请上传头像')
      return
    }

    // 准备注册数据
    const studentData = {
      ...studentForm,
      user_type: 2 // 学生类型
    }

    // TODO: 实现实际的学生注册逻辑
    console.log('学生注册数据:', studentData)
    ElMessage.success('Student registration submitted successfully')

    // 注册成功后可以跳转到登录页面或其他页面
    // currentStep.value = 1 // 跳转到登录页面

  } catch (error) {
    console.error('Student registration error:', error)
    ElMessage.error('Student registration failed, please check the registration information')
  }
}



// 组件挂载时检查是否已登录
onMounted(() => {
  if (authStore.isAuthenticated) {
    const redirectPath = route.query.redirect || '/'
    router.push(redirectPath)
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background-image: url('@/assets/login-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
}

// 品牌标识
.brand-logo {
  position: absolute;
  top: 58px;
  left: 128px;
  z-index: 10;

  .logo {
    width: 248rpx;
    height: 30rpx;
    user-select: none;
  }
}

// 主要内容区域
.main-content {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: flex-end;

}

.illustration-section {
  width: 800px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;

  .illustration {
    width: 100%;
  }
}

.register-section {
  width: 900px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #1C1824;
  border-radius: 10px;

  .select-identity {
    width: 446px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .identity-item {
      width: 208px;
      height: 227px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      flex-direction: column;
      align-items: center;

      &.s-education-provider {
        background-image: url('@/assets/identity-item-1.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .text {
          width: 100%;
          text-align: center;
          margin-top: 20px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 18px;
          color: #FEFEFE;
          user-select: none;
        }
      }

      &.n-student {
        background-image: url('@/assets/identity-item-2.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .text {
          width: 100%;
          text-align: center;
          margin-top: 20px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 18px;
          color: #C4BDC1;
          user-select: none;
        }
      }

      &.n-education-provider {
        background-image: url('@/assets/identity-item-3.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .text {
          width: 100%;
          text-align: center;
          margin-top: 20px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 18px;
          color: #C4BDC1;
          user-select: none;
        }
      }

      &.s-student {
        background-image: url('@/assets/identity-item-4.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;

        .text {
          width: 100%;
          text-align: center;
          margin-top: 20px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          font-size: 18px;
          color: #FEFEFE;
          user-select: none;
        }
      }
    }
  }

  .select-button {
    width: 446px;
    height: 50px;
    font-size: 18px;
    font-weight: 600;
    background: #E34234;
    border: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    margin: 80px 0 20px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(229, 62, 62, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .login-link {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #FEFEFE;
    cursor: pointer;
    user-select: none;
  }
}

// 注册表单样式
.register-section-two {
  width: 900px;
  height: 100vh;
  background-color: #1C1824;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  padding: 0 60px;


  .register-header {
    width: 100%;
    margin: 116px 0 102px;

    h2 {
      font-family: CKTKingkong Bold;
      font-weight: bold;
      font-weight: 400;
      font-size: 30px;
      color: #E34234;

    }
  }

  .register-form {
    .form-section {
      margin-bottom: 37px;

      .agreement {
        font-size: 16px;
        color: #BEBDC1;
      }

      .section-title {
        display: flex;
        align-items: center;
        gap: 4px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 18px;
        color: #FFFFFF;
        margin-bottom: 25px;

        .icon {
          width: 24px;
          height: 24px;
        }
      }

      .form-row {
        display: flex;
        gap: 30px;
        align-items: flex-start;

        .avatar-upload {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;

          .avatar-placeholder {
            width: 74px;
            height: 74px;
            background: #25202F;
            border-radius: 50%;
            border: 1px solid #5F4F55;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;

            .icon {
              width: 24px;
              height: 24px;
            }

            &:hover {
              border-color: #c53030;
              background: rgba(227, 66, 52, 0.1);
            }

            .el-icon {
              font-size: 24px;
              color: #E34234;
            }
          }

          .avatar-label {
            color: #FEFEFE;
            font-size: 16px;
          }

          .avatar-preview {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
          }

          // 头像表单项样式
          .avatar-form-item {
            margin-bottom: 0;

            :deep(.el-form-item__error) {
              position: absolute;
              top: 100%;
              left: 50%;
              transform: translateX(-50%);
              white-space: nowrap;
              background: rgba(245, 108, 108, 0.9);
              color: white;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 12px;
              z-index: 10;
              margin-top: 4px;

              &::before {
                content: '';
                position: absolute;
                top: -4px;
                left: 50%;
                transform: translateX(-50%);
                width: 0;
                height: 0;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-bottom: 4px solid rgba(245, 108, 108, 0.9);
              }
            }
          }
        }

        .form-inputs {
          flex: 1;

          .input-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;

            .el-form-item {
              flex: 1;
              margin-bottom: 0;
            }
          }
        }

        .form-left {
          flex: 1;

          .el-form-item {
            margin-bottom: 20px;
          }
        }

        .form-right {
          width: 374px;

          .upload-section {
            .upload-area {
              width: 100%;
              height: 100px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              background-color: #25202F;
              border-radius: 10px;
              border: 1px solid #5F4F55;
              cursor: pointer;
              transition: all 0.3s ease;

              .upload-file {
                width: 42px;
                height: 42px;
                margin-bottom: 10px;
              }

              .text {
                font-weight: 400;
                font-size: 16px;
                color: #FFFFFF;
              }

              &:hover {
                border-color: #c53030;
                background: rgba(227, 66, 52, 0.1);
              }
            }

            .upload-hint {
              font-weight: 400;
              font-size: 16px;
              line-height: 22px;
              color: #5F4F55;
              margin: 8px 0;
            }

            .verification-section {
              width: 100%;
              height: 60px;
              display: flex;
              align-items: center;
              padding-left: 11px;
              background: #25202F;
              border-radius: 10px;
              border: 1px dashed #5F4F55;
              margin-bottom: 20px;
              color: #BEBDC1;
              font-size: 16px;

              .el-link {
                color: #D64034;
                margin-left: 5px;
              }
            }

            .nft-button {
              width: 100%;
              background: #E34234;
              border: none;
              border-radius: 8px;
              height: 40px;

              &:hover {
                background: #c53030;
              }
            }
          }
        }
      }
    }

    .submit-button {
      width: 100%;
      height: 50px;
      background: #E34234;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 600;
      margin-top: 20px;

      &:hover {
        background: #c53030;
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(227, 66, 52, 0.3);
      }
    }

    .el-date-editor .el-icon-date {
      display: none;
    }

    // 统一输入框样式
    :deep(.el-input__wrapper) {
      background: #25202F;
      border: 1px solid #5F4F55;
      border-radius: 10px;
      box-shadow: none;
      height: 40px;

      &:hover {
        border-color: #E34234;
      }

      &.is-focus {
        border-color: #E34234;
        box-shadow: 0 0 0 2px rgba(227, 66, 52, 0.2);
      }
    }

    :deep(.el-input__inner) {
      color: white;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }
    }

    :deep(.el-select) {
      width: 100%;

      .el-select__wrapper {
        background: #25202F;
        border: 1px solid #5F4F55;
        border-radius: 10px;
        box-shadow: none;
        height: 40px;

        &:hover {
          border-color: #E34234;
        }

        &.is-focus {
          border-color: #E34234;
          box-shadow: 0 0 0 2px rgba(227, 66, 52, 0.2);
        }
      }
    }

    :deep(.el-checkbox) {
      color: #FEFEFE;

      .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color: #E34234;
        border-color: #E34234;
      }

      .el-checkbox__inner:hover {
        border-color: #E34234;
      }
    }

    :deep(.el-form-item__error) {
      color: #E34234;
    }
  }
}

.register-section-three {
  width: 900px;
  height: 100vh;
  background-color: #1C1824;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 225px;

  .student-register-header {
    margin-bottom: 100px;

    h2 {
      font-family: CKTKingkong Bold;
      font-weight: bold;
      font-size: 30px;
      color: #E34234;
    }
  }

  .student-form {
    .avatar-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 60px;

      .avatar-upload-container {
        margin-bottom: 10px;

        .student-avatar-placeholder {
          width: 87px;
          height: 87px;
          background: #25202F;
          border-radius: 50%;
          border: 1px solid #5F4F55;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: #E34234;
            background: rgba(227, 66, 52, 0.1);
          }

          .icon {
            width: 24px;
            height: 24px;
          }

          .student-avatar-preview {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
          }
        }
      }

      .avatar-label {
        color: #FEFEFE;
        font-size: 16px;
      }
    }

    .form-inputs-section {
      margin-bottom: 30px;

      .el-form-item {
        margin-bottom: 20px;
      }
    }

    .nft-creation-button {
      width: 450px;
      height: 40px;
      background: #682C31;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      margin-bottom: 20px;
      margin-top: 10px;

      &:hover {
        background: #c53030;
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(227, 66, 52, 0.3);
      }
    }

    .checkbox-section {
      margin-bottom: 30px;

      .el-form-item {
        margin-bottom: 15px;
      }

      .agreement-text,
      .notification-text {
        color: #BEBDC1;
        font-size: 16px;
      }

      .protocol-link {
        color: #E34234;
        margin-left: 5px;
      }
    }

    .student-submit-button {
      width: 100%;
      height: 50px;
      background: #E34234;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 600;

      &:hover {
        background: #c53030;
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(227, 66, 52, 0.3);
      }
    }

    // 学生表单输入框样式
    .student-input {
      :deep(.el-input__wrapper) {
        background: #25202F;
        border: 1px solid #5F4F55;
        border-radius: 10px;
        box-shadow: none;
        height: 40px;

        &:hover {
          border-color: #E34234;
        }

        &.el-input__prefix {
          display: none !important;
        }

        &.is-focus {
          border-color: #E34234;
          box-shadow: 0 0 0 2px rgba(227, 66, 52, 0.2);
        }
      }

      .input-icon {
        width: 24px;
        height: 24px;
      }

      :deep(.el-input__inner) {
        color: white;
        font-size: 16px;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }
      }

      .input-icon {
        color: #5F4F55;
        font-size: 16px;
      }
    }

    .date-picker-input {
      width: 100%;
      display: flex;
      align-items: center;
      background: #25202F;
      border: 1px solid #5F4F55;
      border-radius: 10px;
      box-shadow: none;
      height: 40px;
      padding: 1px 15px;

      .input-icon {
        width: 24px;
        height: 24px;
      }

      &:hover {
        border-color: #E34234;
      }
    }

    // 日期选择器样式 - 完全清除默认样式
    :deep(.el-date-editor) {
      width: 100%;

      .el-input__wrapper {
        background: transparent !important;
        border: none !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        padding: 0 !important;
        height: auto !important;

        &:hover {
          background: transparent !important;
          border: none !important;
          box-shadow: none !important;
        }

        &.is-focus {
          background: transparent !important;
          border: none !important;
          box-shadow: none !important;
        }

        // 隐藏所有图标
        .el-input__prefix,
        .el-input__suffix {
          display: none !important;
        }
      }
    }

    // 复选框样式
    :deep(.el-checkbox) {
      color: #FEFEFE;

      .el-checkbox__input.is-checked .el-checkbox__inner {
        background-color: #E34234;
        border-color: #E34234;
      }

      .el-checkbox__inner:hover {
        border-color: #E34234;
      }
    }

    :deep(.el-form-item__error) {
      color: #E34234;
    }
  }
}
</style>
