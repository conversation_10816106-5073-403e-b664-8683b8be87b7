<template>
  <div class="webdesign-page">
    <!-- 如果有子路由，显示子路由内容 -->
    <router-view v-if="$route.matched.length > 1" />
    <!-- 如果没有子路由，显示默认内容 -->
    <template v-else>
      <!-- 顶部分类标签 -->
      <div class="category-tabs">
        <div class="tab-item" v-for="category in categories" :key="category.key"
          :class="{ active: activeCategory === category.key }" @click="setActiveCategory(category.key)">
          {{ category.label }}
        </div>
      </div>

      <!-- 筛选器 -->
      <div class="filters">
        <div class="filter-group" v-for="filter in filterOptions" :key="filter.key">
          <el-dropdown @command="(value) => handleFilterChange(filter.key, value)" class="filter-dropdown">
            <span class="filter-button">
              {{ getFilterLabel(filter) }}
              <el-icon class="dropdown-icon">
                <ArrowDown />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="option in filter.options" :key="option.value" :command="option.value"
                  :class="{ active: filters[filter.key] === option.value }">
                  {{ option.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 主要内容 -->
      <div class="content-area">
        <div class="featured-content">
          <!-- 轮播 -->
          <el-carousel class="featured-carousel" height="323" indicator-position="outside" arrow="hover"
            :autoplay="true" :interval="5000">
            <el-carousel-item v-for="item in carouselItems" :key="item.id" @click="goToDetails(item.id)">
              <div class="carousel-slide" :style="{ backgroundImage: `url(${item.image})` }">
                <div class="carousel-content">
                  <h2>{{ item.title }}</h2>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
          <!-- 右侧卡片 -->
          <div class="side-cards">
            <div class="side-card" v-for="item in sideContent" :key="item.id"
              :style="{ backgroundImage: `url(${item.image})` }" @click="goToDetails(item.id)">
              <div class="side-content">
                <h2>{{ item.title }}</h2>
                <div class="time">
                  <el-icon color="#FFF">
                    <Clock />
                  </el-icon>
                  <span class="text">2 hours ago</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 内容网格 -->
        <div class="content-grid">
          <div class="content-card" v-for="item in contentItems" :key="item.id" @click="goToDetails(item.id)">
            <div class="card-image" :style="{ backgroundImage: `url(${item.image})` }"></div>
            <div class="card-content">
              <h3>{{ item.title }}</h3>
              <div class="card-meta">
                <div class="author-info">
                  <img :src="item.author.avatar" :alt="item.author.name" class="author-avatar">
                  <div class="card-stats">
                    <span class="author-name">{{ item.author.name }}</span>
                    <div class="week-view">
                      <div class="stat-item">
                        <el-icon>
                          <Clock />
                        </el-icon>
                        <span>{{ item.timeAgo }}</span>
                      </div>
                      <div class="stat-item">
                        <el-icon>
                          <View />
                        </el-icon>
                        <span>{{ item.views }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowDown, Clock, View } from '@element-plus/icons-vue'
import logo from '@/assets/generated.png';
import logo1 from '@/assets/1.jpg';
import logo2 from '@/assets/2.jpg';
import logo3 from '@/assets/3.jpg';
import logo4 from '@/assets/4.jpg';
import logo5 from '@/assets/5.jpg';
import logo6 from '@/assets/6.jpg';
import logo7 from '@/assets/7.jpg';
import logo8 from '@/assets/8.jpg';
import logo9 from '@/assets/9.png';
import avatar from '@/assets/avatar.png';
import avatar1 from '@/assets/avatar1.png';
// 获取路由实例
const router = useRouter()

// 分类数据
const categories = ref([
  { key: 'all', label: 'All' },
  { key: 'mathematic', label: 'Mathematic' },
  { key: 'ontology', label: 'Ontology' },
  { key: 'chemistry', label: 'Chemistry' },
  { key: 'medical', label: 'Medical Science' }
])

// 当前激活的分类
const activeCategory = ref('all')

// 筛选器数据
const filters = reactive({
  discipline: '',
  contentFormat: '',
  uploadTime: '',
  hostLanguage: ''
})

// 筛选器选项配置
const filterOptions = ref([
  {
    key: 'discipline',
    placeholder: 'Discipline',
    options: [
      { label: 'All Disciplines', value: '' },
      { label: 'Computer Science', value: 'cs' },
      { label: 'Mathematics', value: 'math' },
      { label: 'Physics', value: 'physics' }
    ]
  },
  {
    key: 'contentFormat',
    placeholder: 'Content Format',
    options: [
      { label: 'All Formats', value: '' },
      { label: 'Video', value: 'video' },
      { label: 'Article', value: 'article' },
      { label: 'Podcast', value: 'podcast' }
    ]
  },
  {
    key: 'uploadTime',
    placeholder: 'Upload Time',
    options: [
      { label: 'All Time', value: '' },
      { label: 'Last Week', value: 'week' },
      { label: 'Last Month', value: 'month' },
      { label: 'Last Year', value: 'year' }
    ]
  },
  {
    key: 'hostLanguage',
    placeholder: 'Host Language',
    options: [
      { label: 'All Languages', value: '' },
      { label: 'English', value: 'en' },
      { label: 'Chinese', value: 'zh' },
      { label: 'Spanish', value: 'es' }
    ]
  }
])


// 轮播数据
const carouselItems = ref([
  {
    id: 1,
    title: 'Exploring the Applications of Decision Tree Algorithms in Multi-Domain Classification Tasks',
    image: logo,
  },
  {
    id: 2,
    title: 'Advanced Machine Learning Techniques in Data Science',
    image: logo1,
  },
  {
    id: 3,
    title: 'Deep Learning Applications in Computer Vision',
    image: logo2,
  }
])

// 侧边内容
const sideContent = ref([
  {
    id: 1,
    title: 'Educational communication',
    image: logo1,
    timeAgo: '2 hours ago'
  },
  {
    id: 2,
    title: 'Regression Decision Tree susting in data mining',
    image: logo2,
    timeAgo: '2 hours ago'
  },
  {
    id: 3,
    title: 'Progress of pharmaceutical research and test',
    image: logo3,
    timeAgo: '2 hours ago'
  },
  {
    id: 4,
    title: 'Knowledge Writing. Read',
    image: logo1,
    timeAgo: '2 hours ago'
  }
])

// 内容项目
const contentItems = ref([
  {
    id: 1,
    title: 'Regression Decision Treesusting in data mining',
    image: logo,
    author: {
      name: 'UOW Podcast',
      avatar: avatar
    },
    type: 'Podcast',
    timeAgo: '2 weeks ago',
    views: '86.6k'
  },
  {
    id: 2,
    title: 'Regression Decision Treesusting in data mining',
    image: logo4,
    author: {
      name: 'UOW Podcast',
      avatar: avatar1
    },
    type: 'Podcast',
    timeAgo: '2 weeks ago',
    views: '86.6k'
  },
  {
    id: 3,
    title: 'Seedling development law',
    image: logo5,
    author: {
      name: 'UOW Podcast',
      avatar: avatar
    },
    type: 'Podcast',
    timeAgo: '2 weeks ago',
    views: '86.6k'
  },
  {
    id: 4,
    title: 'Regression Decision Treesusting in data mining',
    image: logo6,
    author: {
      name: 'UOW Podcast',
      avatar: avatar1
    },
    type: 'Podcast',
    timeAgo: '2 weeks ago',
    views: '86.6k'
  },
  {
    id: 5,
    title: 'Regression Decision Treesusting in data mining',
    image: logo7,
    author: {
      name: 'UOW Podcast',
      avatar: avatar1
    },
    type: 'Podcast',
    timeAgo: '2 weeks ago',
    views: '86.6k'
  }
])

// 设置激活分类
const setActiveCategory = (category) => {
  activeCategory.value = category
  console.log('切换到分类:', category)
}

// 处理筛选器变化
const handleFilterChange = (filterKey, value) => {
  filters[filterKey] = value
  console.log('筛选器变化:', filterKey, value)
}

// 获取筛选器显示标签
const getFilterLabel = (filter) => {
  const currentValue = filters[filter.key]
  if (!currentValue) {
    return filter.placeholder
  }
  const selectedOption = filter.options.find(option => option.value === currentValue)
  return selectedOption ? selectedOption.label : filter.placeholder
}

// 跳转到详情页
const goToDetails = (id) => {
  console.log('跳转到详情页，ID:', id)
  router.push('/weDesign/details')
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;

.webdesign-page {
  padding: 20px;
  background: #302D3D;
  border-radius: 6px;
  margin-right: 20px;
  margin-bottom: 20px;

  .category-tabs {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    padding: 4px;

    .tab-item {
      height: 40px;
      display: flex;
      align-items: center;
      padding: 0 20px;
      cursor: pointer;
      border-radius: 6px;
      transition: all 0.3s ease;
      font-weight: 400;
      font-size: 18px;
      line-height: 32px;
      color: #FEFEFE;
      background-color: #414055;

      &:hover {
        background: #414055;
        color: #E34234;
      }

      &.active {
        background: #4F2730;
        color: #E34234;
      }
    }
  }

  .filters {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;

    .filter-group {
      .filter-dropdown {
        .filter-button {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 8px 12px;
          border: none;
          backdrop-filter: blur(10px);
          color: #BDBCC4;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 14px;

          &:hover {
            background: rgba(255, 255, 255, 0.15);
          }

          .dropdown-icon {
            color: rgba(255, 255, 255, 0.6);
            margin-left: 8px;
            transition: transform 0.3s ease;
          }
        }

        &.is-active .filter-button .dropdown-icon {
          transform: rotate(180deg);
        }
      }

      :deep(.el-dropdown-menu__item) {
        &:hover {
          background: rgba(227, 66, 52, 0.2) !important;
        }
      }
    }
  }

  .content-area {
    width: 100%;
    display: flex;
    flex-direction: column;

    .featured-content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      gap: 20px;
      margin-bottom: 24px;

      .featured-carousel {
        width: 100%;
        height: 323px;
        border-radius: 6px;
        position: relative;

        :deep(.el-carousel__container) {
          height: 323px !important;
        }

        :deep(.el-carousel__item) {
          border-radius: 6px;
          overflow: hidden;
        }

        :deep(.el-carousel__arrow) {
          background: rgba(0, 0, 0, 0.5);
          border: none;
          color: white;

          &:hover {
            background: rgba(0, 0, 0, 0.7);
          }
        }

        // 自定义指示器样式
        :deep(.el-carousel__indicators) {
          position: absolute;
          bottom: 16px;
          right: 16px;
          left: auto;
          transform: none;
          display: flex;
          gap: 8px;

          .el-carousel__indicator {
            width: 8px;
            height: 8px;
            padding: 0;
            margin: 0;

            .el-carousel__button {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              background: rgba(255, 255, 255, 0.4);
              border: none;
              transition: all 0.3s ease;

              &:hover {
                background: rgba(255, 255, 255, 0.6);
                transform: scale(1.2);
              }
            }

            &.is-active .el-carousel__button {
              background: white;
              transform: scale(1.2);
            }
          }
        }

        .carousel-slide {
          width: 100%;
          height: 323px;
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          position: relative;
          border-radius: 6px;
          cursor: pointer;

          .carousel-content {
            width: 100%;
            height: 323px;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            box-sizing: border-box;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.4) 100%);

            h2 {
              font-size: 24px;
              font-weight: 600;
              line-height: 1.3;
              color: white;
              max-width: 90%;
              margin: 0px 20px 32px;
            }
          }
        }
      }

      .side-cards {
        width: 100%;
        display: grid;
        gap: 20px;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));

        .side-card {
          height: 151px;
          display: flex;
          flex-direction: column;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          overflow: hidden;
          transition: all 0.3s ease;

          h2 {
            font-weight: 500;
            font-size: 18px;
            color: #FFFFFF;
            padding: 12px;
          }

          .side-content {
            width: 100%;
            height: 151px;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            box-sizing: border-box;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.4) 100%);
            cursor: pointer;

            .time {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              padding: 15px 13px;

              .text {
                font-weight: 400;
                font-size: 16px;
                color: #FFFFFF;
                margin-left: 8px;
              }
            }
          }

          &:hover {
            transform: translateY(-2px);

            .side-content {
              background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.7) 100%);

            }
          }
        }
      }
    }

    .content-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(296px, 1fr));
      gap: 20px;

      .content-card {
        padding: 10px;
        background: #414055;
        border-radius: 16px;
        overflow: hidden;
        backdrop-filter: blur(10px);
        border: 1px solid #5F4F55;
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
          background: rgba(255, 255, 255, 0.15);
          transform: translateY(-4px);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .card-image {
          width: 100%;
          height: 183px;
          border-radius: 10px;
          background-size: cover;
          background-position: center;
          background-color: #4a90e2;
        }

        .card-content {
          padding: 18px 0 2px;
          display: flex;
          flex-direction: column;

          h3 {
            min-height: 55px;
            font-weight: 500;
            font-size: 18px;
            color: #FFFFFF;
            margin: 0 0 16px 0;
          }

          .card-meta {
            .author-info {
              display: flex;
              align-items: center;
              gap: 8px;

              .author-avatar {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                object-fit: cover;
              }

              .author-name {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.9);
                font-weight: 500;
              }

              .content-type {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.6);
                background: rgba(255, 255, 255, 0.1);
                padding: 2px 8px;
                border-radius: 12px;
                margin-left: auto;
              }
            }

            .card-stats {
              width: 100%;
              display: flex;
              flex-direction: column;

              .week-view {
                display: flex;
                justify-content: space-between;
              }

              .stat-item {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 12px;
                color: rgba(255, 255, 255, 0.7);

                .el-icon {
                  font-size: 14px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
