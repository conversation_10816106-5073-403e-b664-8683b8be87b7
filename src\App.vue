<template>
  <div id="app">
    <!-- 登录/注册页面 - 不需要布局和认证 -->
    <template v-if="isLoginPage">
      <router-view />
    </template>

    <!-- 主应用布局 - 需要认证 -->
    <template v-else-if="authStore.isAuthenticated">
      <el-container class="app-layout" direction="vertical">
        <!-- 头部 - 固定在顶部，宽度100% -->
        <AppHeader />

        <!-- 下方内容区域 -->
        <el-container class="content-container">
          <!-- 左侧边栏 -->
          <AppSidebar />

          <!-- 主要内容区域 -->
          <el-main class="app-main">
            <!-- 面包屑导航 -->
            <AppBreadcrumb />
            <!-- 路由视图 -->
            <router-view v-slot="{ Component }">
              <transition name="el-zoom-in-left" mode="out-in" leave-active-class="">
                <component :is="Component" :key="$route.path" />
              </transition>
            </router-view>
          </el-main>
        </el-container>
      </el-container>
    </template>

    <!-- 加载状态或未认证状态 -->
    <template v-else>
      <div class="loading-container" v-loading="true" element-loading-text="正在加载...">
      </div>
    </template>
  </div>
</template>

<script setup>
// 导入Vue组合式API
import { onMounted, watch, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 导入布局组件
import AppHeader from '@/components/Header.vue'
import AppSidebar from '@/components/Sidebar.vue'
import AppBreadcrumb from '@/components/Breadcrumb.vue'

// 获取路由实例和认证store
const route = useRoute()
const authStore = useAuthStore()

// 计算是否为登录/注册页面（不需要认证的页面）
const isLoginPage = computed(() => route.path === '/login' || route.path === '/register')

// 组件挂载时执行
onMounted(() => {
  console.log('WeMaster™ 应用已启动')
  console.log('当前路由:', route.path)

  // 初始化认证状态
  authStore.initAuth()
})

// 监听路由变化
watch(
  () => route.path,
  (newPath, oldPath) => {
    console.log('路由从', oldPath, '跳转到', newPath)
  }
)
</script>

<style lang="scss">
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  line-height: 1.6;
}

#app {
  height: 100vh;
  width: 100%;
  background-image: url('@/assets/app-back.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

/* 布局样式 */
.app-layout {
  height: 100vh;
  width: 100%;
}

.content-container {
  flex: 1;
  height: calc(100vh - 80px);
}

.app-main {
  flex: 1;
  overflow-y: auto;
}

.router-view {
  // background: #302D3D;
  // border-radius: 6px;
  margin-right: 20px;
  margin-bottom: 20px;
  // margin-right: 20px !important;
}

.loading-container {
  height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #201F2F;
}

/* 主内容区域滚动条样式 */
.app-main::-webkit-scrollbar {
  width: 8px;
}

.app-main::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.app-main::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.app-main::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Element Plus 全局样式覆盖 */
.el-header {
  padding: 0 !important;
  height: 80px !important;
  width: 100% !important;
}

.el-aside {
  overflow: hidden;
}

.el-main {
  padding: 0 !important;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式断点 */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-30 {
  margin-top: 30px;
}
</style>
