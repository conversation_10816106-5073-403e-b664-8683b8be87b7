<template>
  <div class="webdesign-details">
    <div class="top-operation">
      <div class="left-item" @click="goBack">
        <img class="icon" src="@/assets/back-icon.png" alt="Back">
        <span class="text">Back</span>
      </div>
      <div class="right-item">Create</div>
    </div>
    <div class="article-content">
      <h2 class="title">Decision Tree-Based Machine Learning Algorithms and Their Applications in Classification
        Problems</h2>
      <div class="article-type">
        <span class="item">Discipline：Computer Science</span>
        <span class="item"><PERSON><PERSON> languege：Chinese/Enghish</span>
        <span class="item">Uploeded August 21.2024 14：04：45</span>
      </div>
      <div class="classification-list">
        <span class="li-item">Machine Learning</span>
        <span class="li-item">Classification Algorithms</span>
        <span class="li-item">Decision Tree Models</span>
        <span class="li-item">Ensemble Learning</span>
      </div>
      <div class="text-content">Decision trees are a widely used machine learning algorithm known for their simplicity
        and robust
        classification capabilities. This
        paper provides an overview of the fundamental principles of decision trees, including their construction
        process, splitting criteria, and
        pruning techniques. It further explores the practical applications of decision trees in classification tasks,
        such as medical diagnosis,
        financial risk assessment, and customer behavior analysis. Through experimental comparisons, the paper analyzes
        the advantages and limitations of decision trees, suggesting improvements like integrating ensemble methods
        (e.g., random forests and gradient boosting trees)
        to enhance accuracy and stability. This study aims to serve as a
        comprehensive reference for researchers and practitioners seeking an
        intuitive yet effective classification tool.</div>
      <img class="illustration" :src="logo2" alt="Iamge">
      <div class="text-content">Decision trees are a widely used machine learning algorithm known for their simplicity
        and robust
        classification capabilities. This
        paper provides an overview of the fundamental principles of decision trees, including their construction
        process, splitting criteria, and
        pruning techniques. It further explores the practical applications of decision trees in classification tasks,
        such as medical diagnosis,
        financial risk assessment, and customer behavior analysis. Through experimental comparisons, the paper analyzes
        the advantages and limitations of decision trees, suggesting improvements like integrating ensemble methods
        (e.g., random forests and gradient boosting trees)
        to enhance accuracy and stability. This study aims to serve as a
        comprehensive reference for researchers and practitioners seeking an
        intuitive yet effective classification tool.</div>
    </div>
    <el-backtop :visibility-height="3">
      <div style="
          height: 100%;
          width: 100%;
          background-color: var(--el-bg-color-overlay);
          box-shadow: var(--el-box-shadow-lighter);
          text-align: center;
          line-height: 40px;
          color: #E34234;
        ">
        UP
      </div>
    </el-backtop>
  </div>
</template>

<script setup>
import logo2 from '@/assets/2.jpg';
import { useRouter } from 'vue-router'

// 获取路由实例
const router = useRouter()

// 返回上一页
const goBack = () => {
  router.push('/weDesign')
}
</script>

<style lang="scss" scoped>
.webdesign-details {
  width: 100%;

  .top-operation {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0px 1px 0px 0px #504E59;

    .left-item {
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;



      .icon {
        width: 22px;
        height: 22px;
        margin-right: 10px;
      }

      .text {
        font-weight: 400;
        font-size: 18px;
        color: #FFFFFF;
      }

      &:hover {
        opacity: 0.8;
        transform: translateX(-2px);
      }
    }

    .right-item {
      width: 109px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 18px;
      color: #E34234;
      background: rgba(227, 66, 52, 0.1);
      border-radius: 10px;
      border: 1px solid #DB4235;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        color: #FFF;
        background-color: #E34234;
      }
    }
  }

  .article-content {
    max-width: 985px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;

    .title {
      width: 100%;
      text-align: justify;
      margin: 28px 0 30px;
      font-weight: 500;
      font-size: 30px;
      color: #FFFFFF;
    }

    .article-type {
      width: 100%;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-top: 1px solid #999999;
      border-bottom: 1px solid #999999;
      padding: 0 15px;

      .item {
        font-weight: 400;
        font-size: 16px;
        color: #999999;
      }
    }

    .classification-list {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 20px;
      flex-wrap: wrap;
      margin: 12px 0 40px;

      .li-item {
        padding: 0 18px;
        height: 30px;
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #C2C2C6;
        background-color: #25202F;
        border-radius: 10px;
      }
    }

    .text-content {
      font-weight: 400;
      font-size: 16px;
      color: #FFFFFF;
      text-align: justify;
    }

    .illustration {
      width: 100%;
      margin: 20px 0;
    }
  }
}
</style>
