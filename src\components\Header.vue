<template>
  <!-- 头部导航栏 -->
  <el-header class="app-header">
    <div class="header-content">
      <!-- 左侧Logo和标题 -->
      <div class="header-left">
        <div class="logo-section">
          <img class="logo" src="@/assets/header-logo.png" />
        </div>
        <div>
        </div>
      </div>
      <!-- 右侧用户信息和操作 -->
      <div class="header-right">
        <!-- 帮助按钮 -->
        <el-tooltip content="帮助" placement="bottom">
          <div circle class="header-btn">
            <img class="icon" src="@/assets/header-help.png" />
          </div>
        </el-tooltip>

        <!-- 通知按钮 -->
        <el-badge :value="notificationCount" class="notification-badge">
          <el-tooltip content="通知" placement="bottom">
            <div circle class="header-btn">
              <img class="icon" src="@/assets/header-notification.png" />
            </div>
          </el-tooltip>
        </el-badge>
        <!-- 分割线 -->
        <div class="dividing-line"></div>
        <!-- 用户头像和信息 -->
        <div class="user-dropdown">
          <div class="user-avatar">
            {{ userInfo.name.charAt(0) }}
          </div>
          <el-dropdown @command="handleUserCommand">
            <div class="user-info">
              <span class="user-name">{{ userInfo.name }}</span>
              <el-icon class="dropdown-icon">
                <ArrowDown />
              </el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon>
                    <User />
                  </el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon>
                    <Setting />
                  </el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon>
                    <SwitchButton />
                  </el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
  </el-header>
</template>

<script setup>
// 导入Vue组合式API
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

// 获取路由和认证store
const router = useRouter()
const authStore = useAuthStore()

// 用户信息（从store获取）
const userInfo = computed(() => {
  if (authStore.userInfo) {
    return {
      name: authStore.userInfo.username || 'User',
      avatar: authStore.userInfo.avatar || '',
      email: authStore.userInfo.email || ''
    }
  }
  return {
    name: 'Guest',
    avatar: '',
    email: ''
  }
})

// 通知数量（模拟数据）
const notificationCount = 3

// 处理用户下拉菜单
const handleUserCommand = async (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能开发中...')
      break
    case 'settings':
      ElMessage.info('设置功能开发中...')
      break
    case 'logout':
      await handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 执行登出
    authStore.logout()
    ElMessage.success('已退出登录')

    // 跳转到登录页
    router.push('/login')
  } catch (error) {
    // 用户取消登出
    console.log('用户取消登出')
  }
}
</script>

<style lang="scss" scoped>
@use '@/styles/variables.scss' as *;

.app-header {
  background-image: linear-gradient(to bottom,
      rgba(32, 31, 47, 1),
      rgba(46, 33, 47, 1));
  box-shadow: 0px 1px 0px 0px #2E2D3C;
  height: $header-height;
  width: 100%;
  position: relative;
  z-index: 1000;
}

.header-content {
  @include flex-between;
  height: 100%;
  padding: 0 $spacing-xl;
  max-width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;

  .logo {
    width: 185px;
    height: 22px;
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  left: 20px;
}

.header-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #414055;
  border-radius: 10px;

  .icon {
    width: 28px;
    height: 28px;
  }
}


.dividing-line {
  width: 1px;
  height: 26px;
  border: 1px solid #414055;
}

.user-dropdown {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 0 15px 0 30px;
  height: 40px;
  background: #414055;
  border-radius: 10px;
  position: relative;
  right: 20px;
  transition: background-color 0.3s;
}


.user-avatar {
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: aquamarine;
  border-radius: 42px;
  z-index: 10;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: white;
  margin-right: 8px;
}

.dropdown-icon {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}
</style>
