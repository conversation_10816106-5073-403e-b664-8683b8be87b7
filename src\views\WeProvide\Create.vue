<template>
  <div class="create-course-container">
    <div class="upload-label">
      <span class="upload-icon"></span>
      <span class="text">Upload locally</span>
    </div>
    <div class="cover-upload-area" @click="handleCoverUpload">
      <div v-if="!coverImage" class="upload-placeholder">
        <img src="@/assets/weprovide-create-1.png" alt="icon">
        <span class="upload-text">Upload cover</span>
      </div>
      <img v-else :src="coverImage" alt="Cover" class="cover-preview" />
    </div>
    <div class="file-upload-section">
      <div class="upload-label">
        <span class="upload-icon"></span>
        <span class="text">Upload locally</span>
      </div>

      <!-- 文件类型标签 -->
      <div class="file-type-tabs">
        <div v-for="tab in fileTabs" :key="tab.type" :class="['tab-item', { active: activeTab === tab.type }]"
          @click="activeTab = tab.type">
          {{ tab.label }}
        </div>
      </div>

      <!-- 文件上传区域 -->
      <div class="upload-area" @click="handleFileUpload">
        <img class="plus-icon" src="@/assets/add-weprovide.png" alt="icon">
        <span class="upload-text">Upload files</span>
      </div>
    </div>

    <div class="content-section">
      <div class="course-preview-section">
        <!-- 图片 -->
        <div v-for="course in previewCourses" :key="course.id" class="course-preview-item">
          <img :src="course.image" :alt="course.title" class="course-image" />
          <div class="course-info">
            <h4 class="course-title">{{ course.title }}</h4>
          </div>
        </div>
        <!-- 视频 -->
        <div v-for="course in previewVideos" :key="course.id" class="course-preview-item">
          <div class="video-container">
            <video class="course-video" controls preload="metadata">
              <source :src="course.src" type="video/mp4" />
              Your browser does not support HTML5 video playback.
            </video>
          </div>
          <div class="course-info">
            <h4 class="course-title">{{ course.title }}</h4>
          </div>
        </div>
      </div>

      <!-- PPT附件列表 -->
      <div class="attachments-section">
        <div v-for="attachment in attachments" :key="attachment.id" class="attachment-item">
          <div class="left-section">
            <img src="@/assets/weprovide-create-3.png" alt="icon">
            <span class="attachment-name">{{ attachment.name }}</span>
          </div>
          <img class="delete-icon" @click="removeAttachment(attachment.id)" src="@/assets/weprovide-create-5.png"
            alt="icon">
        </div>
      </div>
    </div>
    <!-- 表单区域 -->
    <div class="form-section">
      <el-form :model="courseForm" :rules="formRules" ref="formRef" class="course-form">
        <!-- 学科领域 -->
        <div class="form-group">
          <label class="form-label">Discipline</label>
          <el-input v-model="courseForm.discipline" placeholder="Please enter an Discipline" class="form-input" />
        </div>

        <!-- 课程标题 -->
        <div class="form-group">
          <label class="form-label">Title</label>
          <el-input v-model="courseForm.title" placeholder="Please enter an Title" class="form-input" />
        </div>

        <!-- 课程介绍 -->
        <div class="form-group">
          <label class="form-label">Introduction</label>
          <el-input v-model="courseForm.introduction" type="textarea" :rows="6"
            placeholder="Please enter an Introduction" class="form-textarea" />
        </div>

        <!-- 添加标签 -->
        <div class="form-group">
          <label class="form-label">Add tag</label>
          <div class="tag-input-section">
            <div class="default-tags">
              <span v-for="(tag, index) in defaultTags" :key="index" class="default-tag" @click="fillTag(tag)">{{ tag
              }}</span>
            </div>
            <el-input v-model="newTag" placeholder="Please enter Tag" class="tag-input" @keyup.enter="addTag">
              <template #suffix>
                <div class="add-tag-icon" @click="addTag">
                  <img src="@/assets/weprovide-create-4.png" alt="icon">
                </div>
              </template>
            </el-input>
            <div class="custom-tags">
              <p v-for="tag in courseForm.tags" :key="tag" class="custom-tag">
                <span class="text">{{ tag }}</span>
                <img class="remove-tag" src="@/assets/weprovide-create-5.png" @click="removeTag(tag)" alt="icon">
              </p>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button type="primary" class="submit-btn" @click="handleSubmit">
            Submit
          </el-button>
          <el-button class="cancel-btn" @click="handleCancel">
            Cancel
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import logo1 from '@/assets/course-preview-1.jpg';
const video1 = new URL('@/assets/course-video-1.mp4', import.meta.url).href
// 响应式数据
const coverImage = ref('') // 封面图片
const activeTab = ref('Graphics') // 当前选中的文件类型标签
const newTag = ref('') // 新标签输入
const formRef = ref() // 表单引用

// 文件类型标签配置
const fileTabs = [
  { type: 'Graphics', label: 'Graphics' },
  { type: 'Videos', label: 'Videos' },
  { type: 'Slides', label: 'Slides' },
  { type: 'Text', label: 'Text' }
]

// 默认标签配置
const defaultTags = ref([
  'Default tag',
  'Default tag1',
  'Default tag2',
  'Default tag3',
])

// 模拟图片数据
const previewCourses = ref([
  {
    id: 1,
    title: 'Decision Tree-Based Machine Learning Algorithms and Their Applications in Classificati...',
    image: logo1
  }
])

// 模拟视频数据
const previewVideos = ref([
  {
    id: 1,
    title: 'Decision Tree-Based Machine Learning Algorithms and Their Applications in Classificati...',
    src: video1
  }
])


// PPT附件列表
const attachments = ref([
  { id: 1, name: 'Attachment 1.PPT' },
  { id: 2, name: 'Attachment 2.PPT' },
  { id: 3, name: 'Attachment 3.PPT' }
])

// 课程表单数据
const courseForm = reactive({
  discipline: '', // 学科领域
  title: '', // 课程标题
  introduction: '', // 课程介绍
  tags: ['Machine Learning', 'Classification Algorithms', 'Decision Tree Models', 'Ensemble Learning'] // 标签列表
})

// 表单验证规则
const formRules = {
  discipline: [
    { required: true, message: '请输入学科领域', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入课程标题', trigger: 'blur' },
    { min: 5, message: '标题至少5个字符', trigger: 'blur' }
  ],
  introduction: [
    { required: true, message: '请输入课程介绍', trigger: 'blur' },
    { min: 20, message: '介绍至少20个字符', trigger: 'blur' }
  ]
}

// 处理封面上传
const handleCoverUpload = () => {
  // TODO: 实现文件上传逻辑
  console.log('上传封面图片')
  ElMessage.info('封面上传功能待实现')
}

// 处理文件上传
const handleFileUpload = () => {
  // TODO: 实现文件上传逻辑
  console.log('上传文件，当前类型:', activeTab.value)
  ElMessage.info(`上传${activeTab.value}文件功能待实现`)
}

// 删除附件
const removeAttachment = (id) => {
  const attachment = attachments.value.find(item => item.id === id)
  if (!attachment) return

  ElMessageBox.confirm(
    `Are you sure you want to delete "${attachment.name}"?`,
    'Delete Attachment',
    {
      confirmButtonText: 'Delete',
      cancelButtonText: 'Cancel',
      type: 'warning',
      confirmButtonClass: 'el-button--danger'
    }
  ).then(() => {
    const index = attachments.value.findIndex(item => item.id === id)
    if (index > -1) {
      attachments.value.splice(index, 1)
      ElMessage.success('Attachment deleted successfully')
    }
  }).catch(() => {
    // 用户取消删除，不做任何操作
  })
}

// 填充标签到输入框
const fillTag = (tag) => {
  newTag.value = tag
}

// 添加标签
const addTag = () => {
  if (!newTag.value.trim()) {
    ElMessage.warning('请输入标签内容')
    return
  }

  if (courseForm.tags.includes(newTag.value.trim())) {
    ElMessage.warning('标签已存在')
    return
  }

  courseForm.tags.push(newTag.value.trim())
  newTag.value = ''
  ElMessage.success('标签添加成功')
}

// 删除标签
const removeTag = (tag) => {
  const index = courseForm.tags.indexOf(tag)
  if (index > -1) {
    courseForm.tags.splice(index, 1)
    ElMessage.success('标签已删除')
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  try {
    // 验证表单
    await formRef.value.validate()

    // TODO: 实现提交逻辑
    const submitData = {
      ...courseForm,
      coverImage: coverImage.value,
      attachments: attachments.value
    }

    console.log('提交课程数据:', submitData)
    ElMessage.success('课程创建成功！')

  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单内容')
  }
}

// 取消操作
const handleCancel = () => {
  // TODO: 实现取消逻辑，可能需要路由跳转
  console.log('取消创建课程')
  ElMessage.info('已取消创建')
}
</script>

<style lang="scss" scoped>
.create-course-container {
  padding: 30px 309px;
  display: flex;
  flex-direction: column;
  background: #302D3D;
  border-radius: 10px;
  margin-right: 20px;
  margin-bottom: 20px;
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-section {
  width: 100%;
  margin-top: 45px;
}

.upload-label {
  display: flex;
  align-items: center;
  gap: 7px;
  margin-bottom: 30px;

  .upload-icon {
    width: 3px;
    height: 16px;
    background: #E34234;
    border-radius: 1px;
  }

  .text {
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 20px;
    color: #FFFFFF;
  }
}

// 封面上传区域
.cover-upload-area {
  width: 480px;
  height: 255px;
  background: #201F2F;
  border-radius: 10px;
  border: 1px solid #5F4F55;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 30px;

  &:hover {
    border-color: #E34234;
    background: rgba(227, 66, 52, 0.1);
  }

  .upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 13px;

    .cover-icon {
      width: 38px;
      height: 38px;
    }

    .upload-text {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #E34234;
    }
  }

  .cover-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
  }
}

// 文件上传区域
.file-upload-section {
  .file-type-tabs {
    display: flex;
    gap: 11px;
    margin-bottom: 30px;

    .tab-item {
      width: 107px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 10px;
      border: 1px solid #5F4F55;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 400;
      font-size: 16px;
      color: #FFFFFF;

      &:hover {
        border-color: rgba(227, 66, 52, 0.54);
      }

      &.active {
        background: rgba(227, 66, 52, 0.54);
        border-color: rgba(227, 66, 52, 0.54);
      }
    }
  }

  .upload-area {
    width: 100%;
    height: 75px;
    background: #201F2F;
    border-radius: 10px;
    border: 1px solid #5F4F55;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 36px;

    &:hover {
      border-color: #E34234;
      background: rgba(227, 66, 52, 0.1);
    }

    .plus-icon {
      width: 30px;
      height: 30px;
    }

    .upload-text {
      color: #E34234;
      font-size: 16px;
    }
  }
}

// 课程预览区域
.course-preview-section {
  display: flex;
  gap: 20px;

  .course-preview-item {
    position: relative;
    width: 482px;
    min-height: 307px;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;

    .course-image {
      width: 482px;
      height: 271px;
      background: #000000;
      border-radius: 10px;
    }

    .video-container {
      width: 482px;
      height: 271px;
      border-radius: 10px;
      overflow: hidden;
      background: #000000;


      .course-video {
        width: 482px;
        height: 271px;
        padding: 0;
        margin: 0;
      }
    }


    .course-info {
      width: 100%;

      .course-title {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #FFFFFF;
        line-height: 24px;
      }
    }
  }
}

// 附件列表
.attachments-section {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 22px;

  .attachment-item {
    width: 315px;
    height: 71px;
    border-radius: 10px;
    border: 1px solid #5F4F55;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 12px;

    .left-section {
      display: flex;
      align-items: center;
      gap: 13px;

      .attachment-icon {
        width: 51px;
        height: 51px;
      }

      .attachment-name {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #FFFFFF;
      }
    }


    .delete-icon {
      width: 16px;
      height: 16px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.1);
      }
    }
  }
}

// 表单区域
.form-section {
  width: 100%;

  .course-form {
    .form-group {
      margin-bottom: 30px;

      .form-label {
        display: block;
        color: white;
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 10px;
      }

      .form-input,
      .form-textarea {
        :deep(.el-input__wrapper) {
          box-shadow: none;
          height: 40px;
          background: #25202F;
          border-radius: 10px;
          border: 1px solid #5F4F55;
          font-weight: 400;
          font-size: 14px;
          color: #C2C2C6;

          &:hover {
            border-color: #E34234;
          }

          &.is-focus {
            border-color: #E34234;
            box-shadow: 0 0 0 2px rgba(227, 66, 52, 0.2);
          }
        }

        :deep(.el-textarea__inner) {
          box-shadow: none;
          background: #25202F !important;
          border-radius: 10px;
          border: 1px solid #5F4F55;
          font-weight: 400;
          font-size: 14px;
          color: #C2C2C6;

          &:hover {
            border-color: #E34234;
          }

          &.is-focus {
            border-color: #E34234;
            box-shadow: 0 0 0 2px rgba(227, 66, 52, 0.2);
          }
        }

        :deep(.el-input__inner),
        :deep(.el-textarea__inner) {
          color: white;
          background: transparent;

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }
        }
      }
    }

    // 标签输入区域
    .tag-input-section {
      .default-tags {
        display: flex;
        gap: 8px;
        margin-bottom: 15px;

        .default-tag {
          padding: 6px 12px;
          background: #25202F;
          border: 1px solid #5F4F55;
          border-radius: 20px;
          font-size: 12px;
          color: #BEBDC1;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #3A3A3A;
            border-color: #E34234;
            color: #E34234;
          }
        }
      }

      .tag-input {
        margin-bottom: 15px;

        :deep(.el-input__wrapper) {
          height: 40px;
          background: #25202F;
          border-radius: 10px;
          border: 1px solid #5F4F55;
          padding-right: 4px;

          &:hover {
            border-color: #E34234;
          }

          &.is-focus {
            border-color: #E34234;
            box-shadow: 0 0 0 2px rgba(227, 66, 52, 0.2);
          }
        }

        :deep(.el-input__inner) {
          color: white;
          background: transparent;

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }
        }

        .add-tag-icon {
          cursor: pointer;
          width: 60px;
          height: 32px;
          background: rgba(227, 66, 52, 0.35);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;

          .icon {
            width: 22px;
            height: 22px;
          }

          &:hover {
            color: #c53030;
          }
        }
      }

      .custom-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .custom-tag {
          height: 30px;
          border-radius: 10px;
          border: 1px solid #5F4F55;
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 0 20px;

          .text {
            font-weight: 400;
            font-size: 16px;
            color: #5F4F55;
          }

          &:hover {
            color: #ffcccb;
          }

          .remove-tag {
            width: 16px;
            height: 16px;
            cursor: pointer;
          }
        }
      }
    }

    // 操作按钮
    .form-actions {
      display: flex;
      justify-content: center;
      gap: 40px;
      margin: 50px 0 105px;

      .submit-btn {
        width: 208px;
        height: 40px;
        background: #E34234;
        border-radius: 10px;
        border: none;
        color: #FFF;
        font-size: 18px;
        font-weight: 600;

        &:hover {
          background: #c53030;
          transform: translateY(-2px);
          box-shadow: 0 8px 16px rgba(227, 66, 52, 0.3);
        }
      }

      .cancel-btn {
        width: 208px;
        height: 40px;
        border-radius: 10px;
        border: 1px solid #C2C2C6;
        color: white;
        font-size: 16px;
        font-weight: 600;

        &:hover {
          border-color: #E6A23C;
          color: #E6A23C;
          transform: translateY(-2px);
          background: transparent;
        }
      }
    }
  }
}

// 表单验证错误样式
:deep(.el-form-item__error) {
  color: #E34234;
}
</style>
