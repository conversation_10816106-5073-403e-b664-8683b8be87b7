<template>
  <div class="chunk-upload-example">
    <h2>分片上传示例</h2>
    
    <!-- 图片上传 -->
    <div class="upload-section">
      <h3>图片上传</h3>
      <ChunkUpload 
        accept="image/*"
        :chunk-size="1 * 1024 * 1024"
        :max-size="50 * 1024 * 1024"
        @upload-success="handleImageSuccess"
        @upload-error="handleUploadError"
        @upload-progress="handleUploadProgress"
      >
        <template #placeholder>
          <div class="image-placeholder">
            <el-icon size="48"><Picture /></el-icon>
            <p>上传图片文件</p>
            <p class="tip">支持 JPG、PNG、GIF 格式，最大50MB</p>
          </div>
        </template>
      </ChunkUpload>
    </div>

    <!-- 视频上传 -->
    <div class="upload-section">
      <h3>视频上传</h3>
      <ChunkUpload 
        accept="video/*"
        :chunk-size="5 * 1024 * 1024"
        :max-size="500 * 1024 * 1024"
        :concurrent="5"
        @upload-success="handleVideoSuccess"
        @upload-error="handleUploadError"
        @upload-progress="handleUploadProgress"
      >
        <template #placeholder>
          <div class="video-placeholder">
            <el-icon size="48"><VideoPlay /></el-icon>
            <p>上传视频文件</p>
            <p class="tip">支持 MP4、AVI、MOV 格式，最大500MB</p>
          </div>
        </template>
      </ChunkUpload>
    </div>

    <!-- PPT上传 -->
    <div class="upload-section">
      <h3>PPT上传</h3>
      <ChunkUpload 
        accept=".ppt,.pptx,.pdf"
        :chunk-size="2 * 1024 * 1024"
        :max-size="100 * 1024 * 1024"
        @upload-success="handlePptSuccess"
        @upload-error="handleUploadError"
        @upload-progress="handleUploadProgress"
      >
        <template #placeholder>
          <div class="ppt-placeholder">
            <el-icon size="48"><Document /></el-icon>
            <p>上传PPT文件</p>
            <p class="tip">支持 PPT、PPTX、PDF 格式，最大100MB</p>
          </div>
        </template>
      </ChunkUpload>
    </div>

    <!-- 上传结果显示 -->
    <div v-if="uploadResults.length > 0" class="results-section">
      <h3>上传结果</h3>
      <div class="results-list">
        <div 
          v-for="result in uploadResults" 
          :key="result.fileId" 
          class="result-item"
        >
          <div class="result-info">
            <el-icon><SuccessFilled /></el-icon>
            <span class="file-name">{{ result.fileName }}</span>
            <span class="file-size">({{ formatFileSize(result.fileSize) }})</span>
          </div>
          <div class="result-actions">
            <el-button size="small" @click="copyFileId(result.fileId)">
              复制文件ID
            </el-button>
            <el-button size="small" type="danger" @click="deleteFile(result.fileId)">
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传进度统计 -->
    <div v-if="uploadStats.totalFiles > 0" class="stats-section">
      <h3>上传统计</h3>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ uploadStats.totalFiles }}</div>
          <div class="stat-label">总文件数</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ uploadStats.successFiles }}</div>
          <div class="stat-label">成功上传</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ uploadStats.failedFiles }}</div>
          <div class="stat-label">上传失败</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">{{ formatFileSize(uploadStats.totalSize) }}</div>
          <div class="stat-label">总大小</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElIcon, ElButton } from 'element-plus'
import { Picture, VideoPlay, Document, SuccessFilled } from '@element-plus/icons-vue'
import ChunkUpload from './Index.vue'
import { deleteUploadedFile } from '@/api/upload'

// 响应式数据
const uploadResults = ref([])
const uploadStats = reactive({
  totalFiles: 0,
  successFiles: 0,
  failedFiles: 0,
  totalSize: 0
})

// 处理图片上传成功
const handleImageSuccess = (result) => {
  console.log('图片上传成功:', result)
  addUploadResult(result, 'image')
  ElMessage.success(`图片 ${result.fileName} 上传成功！`)
}

// 处理视频上传成功
const handleVideoSuccess = (result) => {
  console.log('视频上传成功:', result)
  addUploadResult(result, 'video')
  ElMessage.success(`视频 ${result.fileName} 上传成功！`)
}

// 处理PPT上传成功
const handlePptSuccess = (result) => {
  console.log('PPT上传成功:', result)
  addUploadResult(result, 'document')
  ElMessage.success(`文档 ${result.fileName} 上传成功！`)
}

// 处理上传错误
const handleUploadError = (error) => {
  console.error('上传失败:', error)
  updateStats('failed')
  ElMessage.error('文件上传失败，请重试')
}

// 处理上传进度
const handleUploadProgress = (progress) => {
  console.log('上传进度:', progress)
  // 可以在这里更新全局进度显示
}

// 添加上传结果
const addUploadResult = (result, type) => {
  uploadResults.value.push({
    ...result,
    type,
    uploadTime: new Date().toLocaleString()
  })
  updateStats('success', result.fileSize)
}

// 更新统计信息
const updateStats = (status, fileSize = 0) => {
  uploadStats.totalFiles++
  if (status === 'success') {
    uploadStats.successFiles++
    uploadStats.totalSize += fileSize
  } else if (status === 'failed') {
    uploadStats.failedFiles++
  }
}

// 复制文件ID
const copyFileId = async (fileId) => {
  try {
    await navigator.clipboard.writeText(fileId)
    ElMessage.success('文件ID已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 删除文件
const deleteFile = async (fileId) => {
  try {
    await deleteUploadedFile(fileId)
    
    // 从结果列表中移除
    const index = uploadResults.value.findIndex(item => item.fileId === fileId)
    if (index > -1) {
      const removedItem = uploadResults.value.splice(index, 1)[0]
      
      // 更新统计
      uploadStats.totalFiles--
      uploadStats.successFiles--
      uploadStats.totalSize -= removedItem.fileSize
    }
    
    ElMessage.success('文件删除成功')
  } catch (error) {
    console.error('删除失败:', error)
    ElMessage.error('文件删除失败')
  }
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style lang="scss" scoped>
.chunk-upload-example {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;

  h2 {
    text-align: center;
    margin-bottom: 40px;
    color: #303133;
  }

  .upload-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 8px;

    h3 {
      margin-bottom: 20px;
      color: #606266;
    }

    .image-placeholder,
    .video-placeholder,
    .ppt-placeholder {
      p {
        margin: 10px 0;
        color: #606266;
      }

      .tip {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .results-section {
    margin-bottom: 40px;
    padding: 20px;
    background: #f5f7fa;
    border-radius: 8px;

    h3 {
      margin-bottom: 20px;
      color: #606266;
    }

    .results-list {
      .result-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        margin-bottom: 10px;
        background: white;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        .result-info {
          display: flex;
          align-items: center;
          gap: 10px;

          .file-name {
            font-weight: 500;
          }

          .file-size {
            color: #909399;
            font-size: 12px;
          }
        }

        .result-actions {
          display: flex;
          gap: 10px;
        }
      }
    }
  }

  .stats-section {
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    color: white;

    h3 {
      margin-bottom: 20px;
      text-align: center;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 20px;

      .stat-item {
        text-align: center;
        padding: 20px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 6px;

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 14px;
          opacity: 0.8;
        }
      }
    }
  }
}
</style>
