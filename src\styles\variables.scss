// 颜色变量
$primary-color: #e34234;
$primary-dark: #c53030;
$primary-light: #f56565;
$secondary-color: #2c3e50;
$background-color: #f5f7fa;
$text-color: #2c3e50;
$text-light: #666;
$text-muted: #999;
$border-color: #e4e7ed;
$white: #ffffff;

// 尺寸变量
$header-height: 80px;
$sidebar-width: 240px;
$sidebar-collapsed-width: 64px;
$border-radius: 8px;
$border-radius-small: 4px;
$border-radius-large: 12px;

// 间距变量
$spacing-xs: 5px;
$spacing-sm: 10px;
$spacing-md: 15px;
$spacing-lg: 20px;
$spacing-xl: 30px;

// 字体变量
$font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
  "Ubuntu", "Cantarell", sans-serif;
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;

// 阴影变量
$shadow-light: 0 2px 8px rgba(0, 0, 0, 0.05);
$shadow-medium: 0 2px 12px rgba(0, 0, 0, 0.08);
$shadow-heavy: 0 4px 16px rgba(0, 0, 0, 0.1);

// 过渡变量
$transition-fast: 0.2s;
$transition-normal: 0.3s;
$transition-slow: 0.5s;

// 响应式断点
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1600px;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin transition($property: all) {
  transition: #{$property} $transition-normal ease;
}

@mixin border-radius($radius: $border-radius) {
  border-radius: $radius;
}

@mixin box-shadow($shadow: $shadow-light) {
  box-shadow: $shadow;
}

// 响应式 Mixins
@mixin mobile {
  @media (max-width: #{$breakpoint-sm - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-md - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-md}) {
    @content;
  }
}
