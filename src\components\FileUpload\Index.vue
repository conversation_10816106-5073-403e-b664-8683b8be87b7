<template>
  <div class="uploading-container">
    <div class="upload-input-div">
      <div class="custom-upload-btn" @click="triggerFileInput">
        <slot name="trigger">
        </slot>
      </div>
      <input ref="fileInputRef" accept="image/*" class="file-upload-input" type="file" @change="uploadFile" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { postUploadFile } from '@/api/upload'

// Emits 定义
const emit = defineEmits(['upload-data'])

// 响应式数据
const fileInputRef = ref()

// 图片上传类型
const accept = 'image/gif, image/jpeg, image/png, image/jpg'

// 触发文件选择
const triggerFileInput = () => {
  fileInputRef.value?.click()
}

// 上传文件 - 图片
const uploadFile = async (event) => {
  const img = event.target.files[0]
  if (!img) return

  const type = img.type // 文件的类型，判断是否是图片
  const size = img.size // 文件的大小，判断图片的大小

  // 文件类型验证
  if (accept.indexOf(type) === -1) {
    ElMessage({
      message: '请选择gif,jpeg,png,jpg格式图片上传',
      type: 'warning'
    })
    return false
  }

  // 文件大小验证 (3MB)
  if (size > 3145728) {
    ElMessage({
      message: '请选择3M以内的图片上传',
      type: 'warning'
    })
    return false
  }

  // 浏览器兼容性检查
  if (typeof FileReader === 'undefined') {
    ElMessage({
      message: '抱歉，你的浏览器不支持 FileReader，请使用现代浏览器操作！',
      type: 'error'
    })
    return false
  }

  // 显示加载状态
  const loading = ElLoading.service({ text: '图片上传中...' })

  try {
    const { code, data } = await postUploadFile(img)


    if (code === 200) {
      const uploadedFileUrl = data.ossUrl
      ElMessage({
        message: '上传成功',
        type: 'success'
      })
      // 发送事件
      emit('upload-data', mockResponse.data)
    } else {
      ElMessage({
        message: mockResponse.message || '上传失败',
        type: 'error'
      })
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage({
      message: '上传失败，请重试',
      type: 'error'
    })
  } finally {
    // 关闭加载状态
    setTimeout(() => {
      loading.close()
    }, 500)

    // 清空input值，允许重复选择同一文件
    event.target.value = ''
  }
}


</script>

<style lang="scss" scoped>
.uploading-container {
  width: 100%;
}

.upload-input-div {
  position: relative;
  padding: 0;
}

// 自定义上传按钮样式
.custom-upload-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  user-select: none;

  .upload-icon,
  .upload-arrow {
    flex-shrink: 0;
  }
}

.file-upload-input {
  display: none;
}

.textTips {
  padding: 0;
  font-size: 12px;
  color: #d40000;
}

.textTips2 {
  padding: 5px 0;
  font-size: 12px;
  line-height: 14px;
  color: #969799;
  margin: 8px 0 0 0;
}
</style>
