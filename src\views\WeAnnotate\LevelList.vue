<template>
  <div class="level-list-container">
    <div class="subjects-grid">
      <div class="subject-card establish-domain">
        <div class="card-content">
          <div class="plus-icon">
            <el-icon size="48">
              <Plus />
            </el-icon>
          </div>
          <h3 class="subject-title">Establish Domain</h3>
        </div>
      </div>
      <div v-for="subject in subjects" :key="subject.id" :style="{ backgroundImage: `url(${subject.image})` }"
        class="subject-template">
        <div class="card-content">
          <div class="subject-title">
            <i class="line"></i>
            <p class="text">{{ subject.title }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import subject1 from '@/assets/subject-1.jpg';
import subject2 from '@/assets/subject-2.jpg';
import subject3 from '@/assets/subject-3.jpg';
import subject4 from '@/assets/subject-4.jpg';
import subject5 from '@/assets/subject-5.jpg';
import subject6 from '@/assets/subject-6.jpg';
import subject7 from '@/assets/subject-7.jpg';
import subject8 from '@/assets/subject-8.jpg';
import subject9 from '@/assets/subject-9.jpg';

// 学科数据
const subjects = ref([
  {
    id: 2,
    image: subject1,
    title: 'Computer Science',
    className: 'computer-science'
  },
  {
    id: 3,
    image: subject2,
    title: 'Mathematics',
    className: 'mathematics'
  },
  {
    id: 4,
    image: subject3,
    title: 'Physics',
    className: 'physics'
  },
  {
    id: 5,
    image: subject4,
    title: 'Biology',
    className: 'biology'
  },
  {
    id: 6,
    image: subject5,
    title: 'Chemistry',
    className: 'chemistry'
  },
  {
    id: 7,
    image: subject6,
    title: 'Psychology',
    className: 'psychology'
  },
  {
    id: 8,
    image: subject7,
    title: 'Economics',
    className: 'economics'
  },
  {
    id: 9,
    image: subject8,
    title: 'Sociology',
    className: 'sociology'
  },
  {
    id: 10,
    image: subject9,
    title: 'AI Intelligence',
    className: 'ai-intelligence'
  }
])

</script>

<style lang="scss" scoped>
.level-list-container {
  padding: 20px;
  min-height: calc(100vh - 154px);
  background: #302D3D;
  border-radius: 6px;
  margin-right: 20px;
  margin-bottom: 20px;

  .subjects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;

    .subject-card {
      height: 270px;
      border-radius: 12px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      }


      &:hover .card-overlay {
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.4) 100%);
      }

      .card-content {
        z-index: 2;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 20px;
        text-align: center;


        .subject-title {
          color: white;
          font-size: 18px;
          font-weight: 600;
          margin: 0;
          text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .plus-icon {
          margin-bottom: 15px;
          color: white;
          opacity: 0.8;
        }
      }

      &.establish-domain {
        background: #25202F;
        border: 2px dashed rgba(255, 255, 255, 0.3);

        .card-content {
          justify-content: center;
        }
      }
    }

    .subject-template {
      height: 270px;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      border-radius: 12px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      }


      &:hover .card-overlay {
        background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.4) 100%);
      }

      .card-content {
        z-index: 2;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        box-sizing: border-box;
        background: rgba(0, 0, 0, 0.6);

        .subject-title {
          display: flex;
          flex-direction: column;
          margin: 0 0 40px 25px;

          .line {
            width: 40px;
            height: 2px;
            background-color: #FFF;
            margin-bottom: 8px;
          }

          .text {
            color: #FFF;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
          }
        }
      }
    }
  }
}
</style>
