import request from "@/utils/request";

// 用户登录
export const loginApi = (data) => {
  return request({
    url: "/auth/login",
    method: "post",
    data,
  });
};

// 退出登录
export const logoutApi = () => {
  return request({
    url: "/auth/logout",
    method: "post",
  });
};

// 刷新token
export const refreshTokenApi = () => {
  return request({
    url: "/auth/refresh",
    method: "post",
  });
};

//获取用户信息
export const getUserInfoApi = () => {
  return request({
    url: "/auth/userinfo",
    method: "get",
  });
};

//修改密码
export const changePasswordApi = (data) => {
  return request({
    url: "/auth/change-password",
    method: "post",
    data,
  });
};

//用户注册
export const registerApi = (data) => {
  return request({
    url: "/auth/register",
    method: "post",
    data,
  });
};

// 忘记密码
export const forgotPasswordApi = (data) => {
  return request({
    url: "/auth/forgot-password",
    method: "post",
    data,
  });
};

// 重置密码
export const resetPasswordApi = (data) => {
  return request({
    url: "/auth/reset-password",
    method: "post",
    data,
  });
};
