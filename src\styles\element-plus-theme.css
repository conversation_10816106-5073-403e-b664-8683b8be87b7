/* Element Plus 主题颜色覆盖 */
:root {
  /* 主要颜色 */
  --el-color-primary: #E34234;
  --el-color-primary-light-1: #e55a4d;
  --el-color-primary-light-2: #e77266;
  --el-color-primary-light-3: #ea8a7f;
  --el-color-primary-light-4: #eca298;
  --el-color-primary-light-5: #eebab1;
  --el-color-primary-light-6: #f0d2ca;
  --el-color-primary-light-7: #f3eae3;
  --el-color-primary-light-8: #f7f2f0;
  --el-color-primary-light-9: #fbf9f8;
  --el-color-primary-dark-1: #cc3b2e;
  --el-color-primary-dark-2: #b53428;
  
  /* 成功颜色 */
  --el-color-success: #67c23a;
  --el-color-success-light-1: #75c947;
  --el-color-success-light-2: #83d054;
  --el-color-success-light-3: #91d761;
  --el-color-success-light-4: #9fde6e;
  --el-color-success-light-5: #ade57b;
  --el-color-success-light-6: #bbec88;
  --el-color-success-light-7: #c9f395;
  --el-color-success-light-8: #d7faa2;
  --el-color-success-light-9: #e5ffaf;
  --el-color-success-dark-1: #5daf34;
  --el-color-success-dark-2: #529b2e;
  
  /* 警告颜色 */
  --el-color-warning: #e6a23c;
  --el-color-warning-light-1: #e9ad4f;
  --el-color-warning-light-2: #ecb862;
  --el-color-warning-light-3: #efc375;
  --el-color-warning-light-4: #f2ce88;
  --el-color-warning-light-5: #f5d99b;
  --el-color-warning-light-6: #f8e4ae;
  --el-color-warning-light-7: #fbefc1;
  --el-color-warning-light-8: #fefad4;
  --el-color-warning-light-9: #fefde7;
  --el-color-warning-dark-1: #cf9236;
  --el-color-warning-dark-2: #b88230;
  
  /* 危险颜色 */
  --el-color-danger: #f56c6c;
  --el-color-danger-light-1: #f67979;
  --el-color-danger-light-2: #f78686;
  --el-color-danger-light-3: #f89393;
  --el-color-danger-light-4: #f9a0a0;
  --el-color-danger-light-5: #faadad;
  --el-color-danger-light-6: #fbbaba;
  --el-color-danger-light-7: #fcc7c7;
  --el-color-danger-light-8: #fdd4d4;
  --el-color-danger-light-9: #fee1e1;
  --el-color-danger-dark-1: #dd6161;
  --el-color-danger-dark-2: #c45656;
  
  /* 信息颜色 */
  --el-color-info: #909399;
  --el-color-info-light-1: #9a9da3;
  --el-color-info-light-2: #a4a7ad;
  --el-color-info-light-3: #aeb1b7;
  --el-color-info-light-4: #b8bbc1;
  --el-color-info-light-5: #c2c5cb;
  --el-color-info-light-6: #cccfd5;
  --el-color-info-light-7: #d6d9df;
  --el-color-info-light-8: #e0e3e9;
  --el-color-info-light-9: #eaedf3;
  --el-color-info-dark-1: #82848a;
  --el-color-info-dark-2: #74767b;
}

/* 深色主题下的颜色覆盖 */
html.dark {
  /* 主要颜色在深色主题下 */
  --el-color-primary: #E34234;
  --el-color-primary-light-1: #e55a4d;
  --el-color-primary-light-2: #e77266;
  --el-color-primary-light-3: #ea8a7f;
  --el-color-primary-light-4: #eca298;
  --el-color-primary-light-5: #eebab1;
  --el-color-primary-light-6: #f0d2ca;
  --el-color-primary-light-7: #f3eae3;
  --el-color-primary-light-8: #f7f2f0;
  --el-color-primary-light-9: #fbf9f8;
  --el-color-primary-dark-1: #cc3b2e;
  --el-color-primary-dark-2: #b53428;
  
  /* 深色主题背景 */
  --el-bg-color: #1a1a1a;
  --el-bg-color-page: #0a0a0a;
  --el-bg-color-overlay: #1d1e1f;
  
  /* 深色主题文本颜色 */
  --el-text-color-primary: #e5eaf3;
  --el-text-color-regular: #cfd3dc;
  --el-text-color-secondary: #a3a6ad;
  --el-text-color-placeholder: #8d9095;
  --el-text-color-disabled: #6c6e72;
  
  /* 深色主题边框颜色 */
  --el-border-color: #4c4d4f;
  --el-border-color-light: #414243;
  --el-border-color-lighter: #363637;
  --el-border-color-extra-light: #2b2b2c;
  --el-border-color-dark: #58585b;
  --el-border-color-darker: #636466;
  
  /* 深色主题填充颜色 */
  --el-fill-color: #303133;
  --el-fill-color-light: #262727;
  --el-fill-color-lighter: #1d1d1d;
  --el-fill-color-extra-light: #191919;
  --el-fill-color-dark: #39393a;
  --el-fill-color-darker: #424243;
  --el-fill-color-blank: transparent;
}

/* 按钮组件特殊样式 */
.el-button--primary {
  --el-button-bg-color: #E34234;
  --el-button-border-color: #E34234;
  --el-button-hover-bg-color: #e55a4d;
  --el-button-hover-border-color: #e55a4d;
  --el-button-active-bg-color: #cc3b2e;
  --el-button-active-border-color: #cc3b2e;
}

/* 链接组件特殊样式 */
.el-link--primary {
  --el-link-text-color: #E34234;
  --el-link-hover-text-color: #e55a4d;
}

/* 输入框焦点状态 */
.el-input__wrapper.is-focus {
  --el-input-focus-border-color: #E34234;
}

/* 复选框选中状态 */
.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #E34234;
  border-color: #E34234;
}

/* 单选框选中状态 */
.el-radio__input.is-checked .el-radio__inner {
  border-color: #E34234;
}

.el-radio__input.is-checked .el-radio__inner::after {
  background-color: #E34234;
}

/* 开关组件 */
.el-switch.is-checked .el-switch__core {
  background-color: #E34234;
  border-color: #E34234;
}

/* 滑块组件 */
.el-slider__runway .el-slider__bar {
  background-color: #E34234;
}

.el-slider__button {
  border-color: #E34234;
}

/* 进度条组件 */
.el-progress-bar__inner {
  background-color: #E34234;
}

/* 标签页组件 */
.el-tabs__active-bar {
  background-color: #E34234;
}

.el-tabs__item.is-active {
  color: #E34234;
}

/* 分页组件 */
.el-pagination .el-pager li.is-active {
  background-color: #E34234;
  color: #fff;
}

/* 菜单组件 */
.el-menu--horizontal .el-menu-item.is-active {
  border-bottom-color: #E34234;
  color: #E34234;
}

.el-menu-item.is-active {
  color: #E34234;
}

/* 步骤条组件 */
.el-step__head.is-process {
  color: #E34234;
  border-color: #E34234;
}

.el-step__head.is-finish {
  color: #E34234;
  border-color: #E34234;
}

/* 加载组件 */
.el-loading-spinner .circular {
  stroke: #E34234;
}

/* 消息提示组件 */
.el-message--success .el-message__icon {
  color: #67c23a;
}

.el-message--warning .el-message__icon {
  color: #e6a23c;
}

.el-message--error .el-message__icon {
  color: #f56c6c;
}
