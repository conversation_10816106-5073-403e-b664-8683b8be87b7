# ChunkUpload 分片上传组件

一个健壮易懂的分片上传组件，支持 PPT、video、image 等多种文件类型的大文件上传。

## 功能特性

- ✅ **分片上传**: 将大文件分割成小块并发上传，提高上传成功率
- ✅ **断点续传**: 支持暂停、继续上传功能
- ✅ **错误重试**: 自动重试失败的分片，提高上传稳定性
- ✅ **进度显示**: 实时显示上传进度和状态
- ✅ **拖拽上传**: 支持拖拽文件到上传区域
- ✅ **文件验证**: 支持文件类型和大小限制
- ✅ **并发控制**: 可配置同时上传的分片数量

## 使用方法

### 基础用法

```vue
<template>
  <ChunkUpload 
    @upload-success="handleUploadSuccess"
    @upload-error="handleUploadError"
    @upload-progress="handleUploadProgress"
  />
</template>

<script setup>
import ChunkUpload from '@/components/ChunkUpload/Index.vue'

const handleUploadSuccess = (result) => {
  console.log('上传成功:', result)
  // result: { fileId, fileName, fileSize }
}

const handleUploadError = (error) => {
  console.error('上传失败:', error)
}

const handleUploadProgress = (progress) => {
  console.log('上传进度:', progress)
  // progress: { progress, uploadedChunks, totalChunks }
}
</script>
```

### 自定义配置

```vue
<template>
  <ChunkUpload 
    accept="image/*,video/*,.ppt,.pptx"
    :chunk-size="5 * 1024 * 1024"
    :max-size="500 * 1024 * 1024"
    :concurrent="5"
    @upload-success="handleUploadSuccess"
  >
    <template #placeholder>
      <div class="custom-placeholder">
        <el-icon size="64"><Upload /></el-icon>
        <h3>上传您的文件</h3>
        <p>支持 PPT、视频、图片等格式</p>
      </div>
    </template>
  </ChunkUpload>
</template>
```

## Props 配置

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| accept | String | `'image/*,video/*,.ppt,.pptx,.pdf,.doc,.docx'` | 接受的文件类型 |
| chunkSize | Number | `2 * 1024 * 1024` | 分片大小（字节），默认2MB |
| maxSize | Number | `100 * 1024 * 1024` | 最大文件大小（字节），默认100MB |
| concurrent | Number | `3` | 并发上传分片数量 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| upload-success | `{ fileId, fileName, fileSize }` | 上传成功时触发 |
| upload-error | `error` | 上传失败时触发 |
| upload-progress | `{ progress, uploadedChunks, totalChunks }` | 上传进度更新时触发 |

## Slots 插槽

| 插槽名 | 说明 |
|--------|------|
| placeholder | 自定义上传区域的占位内容 |

## 后端接口要求

### 1. 分片上传接口 `/file/chunk/upload`

**请求方式**: POST  
**Content-Type**: multipart/form-data

**请求参数**:
```javascript
{
  fileId: "string",      // 文件唯一标识
  chunkNumber: "number", // 分片序号，从0开始
  totalChunks: "number", // 总分片数
  chunk: "File"          // 分片文件数据
}
```

**响应格式**:
```javascript
{
  code: 200,
  message: "success",
  data: {
    chunkNumber: 0,
    uploaded: true
  }
}
```

### 2. 分片合并接口 `/file/chunk/merge`

**请求方式**: POST  
**Content-Type**: application/json

**请求参数**:
```javascript
{
  fileId: "string",      // 文件唯一标识
  fileName: "string",    // 原始文件名
  totalChunks: "number"  // 总分片数
}
```

**响应格式**:
```javascript
{
  code: 200,
  message: "success",
  data: {
    fileId: "string",
    fileName: "string",
    fileUrl: "string",    // 合并后的文件访问地址
    fileSize: "number"
  }
}
```

## 数据结构说明

根据您提供的图片，分片上传的数据结构如下：

- **fileId**: `number` 必填 - 文件唯一标识
- **chunkNumber**: `number` 必填 - 分片序号，从1开始
- **totalChunks**: `number` 必填 - 总分片数
- **chunk**: `null` 必填 - 切片文件文件

## 注意事项

1. **文件ID生成**: 组件会自动生成唯一的文件ID，格式为 `时间戳_随机字符串`
2. **分片大小**: 建议分片大小在1-10MB之间，过小会增加请求次数，过大可能导致上传超时
3. **并发控制**: 并发数量不宜过大，建议3-5个，避免对服务器造成过大压力
4. **错误处理**: 组件会自动重试失败的分片，但需要后端支持幂等性
5. **内存优化**: 组件使用 `File.slice()` 方法创建分片，不会将整个文件加载到内存中

## 浏览器兼容性

- Chrome 21+
- Firefox 13+
- Safari 5.1+
- IE 10+

## 更新日志

### v1.0.0
- 初始版本发布
- 支持分片上传、断点续传、错误重试
- 支持拖拽上传和进度显示
