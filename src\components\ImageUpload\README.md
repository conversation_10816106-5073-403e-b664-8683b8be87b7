# ImageUpload 图片上传组件

一个基于 Element Plus 的强壮完善的图片上传组件，支持多种配置和功能。

## 功能特性

- ✅ **文件格式验证** - 支持自定义允许的图片格式
- ✅ **文件大小限制** - 可配置最大文件大小
- ✅ **数量限制** - 支持单文件或多文件上传
- ✅ **图片压缩** - 可选的客户端图片压缩功能
- ✅ **拖拽上传** - 支持拖拽上传模式
- ✅ **预览功能** - 内置图片预览对话框
- ✅ **删除确认** - 删除文件时的确认提示
- ✅ **错误处理** - 完善的错误提示和处理
- ✅ **自定义样式** - 支持自定义触发器和提示内容
- ✅ **响应式设计** - 适配不同屏幕尺寸

## 基础用法

```vue
<template>
  <ImageUpload v-model="fileList" />
</template>

<script setup>
import { ref } from 'vue'
import ImageUpload from '@/components/ImageUpload.vue'

const fileList = ref([])
</script>
```

## 高级用法

### 多文件上传
```vue
<template>
  <ImageUpload 
    v-model="fileList"
    :limit="5"
    :multiple="true"
    trigger-text="上传多张图片"
  />
</template>
```

### 拖拽上传
```vue
<template>
  <ImageUpload 
    v-model="fileList"
    :drag="true"
    trigger-text="拖拽图片到此处或点击上传"
  />
</template>
```

### 自定义配置
```vue
<template>
  <ImageUpload 
    v-model="fileList"
    action="/api/upload/image"
    :headers="{ Authorization: 'Bearer ' + token }"
    :data="{ folder: 'avatars' }"
    :max-size="10"
    :allowed-formats="['jpg', 'png']"
    :enable-compress="true"
    :compress-quality="0.7"
    @success="handleSuccess"
    @error="handleError"
  />
</template>
```

### 自定义触发器和提示
```vue
<template>
  <ImageUpload v-model="fileList">
    <template #trigger>
      <div class="custom-trigger">
        <el-icon><Camera /></el-icon>
        <span>选择头像</span>
      </div>
    </template>
    
    <template #tip>
      <div class="custom-tip">
        建议上传 200x200 像素的正方形图片
      </div>
    </template>
  </ImageUpload>
</template>
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | Array | [] | 文件列表，支持 v-model |
| action | String | '/api/upload' | 上传地址 |
| headers | Object | {} | 请求头 |
| data | Object | {} | 上传时附带的额外参数 |
| limit | Number | 1 | 最大上传数量 |
| maxSize | Number | 5 | 文件大小限制(MB) |
| accept | String | 'image/*' | 接受的文件类型 |
| allowedFormats | Array | ['jpg', 'jpeg', 'png', 'gif', 'webp'] | 允许的文件格式 |
| disabled | Boolean | false | 是否禁用 |
| showFileList | Boolean | true | 是否显示文件列表 |
| drag | Boolean | false | 是否支持拖拽上传 |
| multiple | Boolean | false | 是否支持多选 |
| triggerText | String | '上传图片' | 触发按钮文本 |
| tipText | String | '' | 提示文本 |
| showTip | Boolean | true | 是否显示提示 |
| autoUpload | Boolean | true | 是否自动上传 |
| enableCompress | Boolean | true | 是否启用图片压缩 |
| compressQuality | Number | 0.8 | 图片压缩质量(0-1) |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| success | (response, file, fileList) | 上传成功时触发 |
| error | (error, file, fileList) | 上传失败时触发 |
| remove | (file, fileList) | 删除文件时触发 |
| preview | (file) | 预览文件时触发 |
| exceed | (files, fileList) | 超出文件数量限制时触发 |
| before-upload | (file) | 上传前触发 |

## Methods 方法

| 方法名 | 参数 | 说明 |
|--------|------|------|
| submit | - | 手动上传文件 |
| clearFiles | - | 清空文件列表 |

## Slots 插槽

| 插槽名 | 说明 |
|--------|------|
| trigger | 自定义触发器内容 |
| tip | 自定义提示内容 |

## 样式定制

组件使用 SCSS 编写样式，支持通过 CSS 变量或深度选择器进行样式定制：

```scss
.image-upload {
  // 自定义上传区域大小
  :deep(.el-upload--picture-card) {
    width: 150px;
    height: 150px;
  }
  
  // 自定义文件列表项大小
  :deep(.el-upload-list__item) {
    width: 150px;
    height: 150px;
  }
}
```

## 注意事项

1. 确保后端上传接口返回正确的响应格式
2. 图片压缩功能在客户端执行，可能会影响性能
3. 大文件上传建议关闭压缩功能
4. 拖拽上传需要现代浏览器支持
5. 删除操作会弹出确认对话框，可通过事件处理自定义逻辑
