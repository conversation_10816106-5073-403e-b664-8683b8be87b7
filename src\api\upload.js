import axios from "axios";
import { useAuthStore } from "@/stores/auth";

// 获取认证token的通用函数
const getAuthToken = () => {
  const authStore = useAuthStore();
  return authStore.getToken || "";
};

// 通用请求配置
const createRequestConfig = (config) => {
  return {
    timeout: 30000,
    headers: {
      token: getAuthToken(),
      ...config.headers,
    },
    ...config,
  };
};

// 表单形式上传图片
export const postUploadFile = async (file) => {
  const formData = new FormData();
  formData.append("file", file);

  const { data } = await axios(
    createRequestConfig({
      method: "post",
      url: import.meta.env.VITE_API_BASE_URL + "/file/common/image/upload",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      timeout: 10000,
    })
  );
  return data;
};

/**
 * 分片上传接口
 * @param {FormData} formData - 包含分片数据的表单
 * @returns {Promise}
 */
export const uploadChunk = async (formData) => {
  const { data } = await axios(
    createRequestConfig({
      method: "post",
      url: import.meta.env.VITE_API_BASE_URL + "/file/chunk/upload",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    })
  );
  return data;
};

/**
 * 合并分片接口
 * @param {Object} params - 合并参数
 * @param {string} params.fileId - 文件ID
 * @param {string} params.fileName - 文件名
 * @param {number} params.totalChunks - 总分片数
 * @returns {Promise}
 */
export const mergeChunks = async (params) => {
  const { data } = await axios(
    createRequestConfig({
      method: "post",
      url: import.meta.env.VITE_API_BASE_URL + "/file/chunk/merge",
      data: {
        fileId: params.fileId,
        fileName: params.fileName,
        totalChunks: params.totalChunks,
      },
      headers: {
        "Content-Type": "application/json",
      },
    })
  );
  return data;
};

/**
 * 检查文件是否已存在（秒传功能）
 * @param {Object} params - 检查参数
 * @param {string} params.fileHash - 文件哈希值
 * @param {string} params.fileName - 文件名
 * @param {number} params.fileSize - 文件大小
 * @returns {Promise}
 */
export const checkFileExists = async (params) => {
  const { data } = await axios(
    createRequestConfig({
      method: "post",
      url: import.meta.env.VITE_API_BASE_URL + "/file/check",
      data: params,
      headers: {
        "Content-Type": "application/json",
      },
    })
  );
  return data;
};

/**
 * 获取已上传的分片列表
 * @param {string} fileId - 文件ID
 * @returns {Promise}
 */
export const getUploadedChunks = async (fileId) => {
  const { data } = await axios(
    createRequestConfig({
      method: "get",
      url: import.meta.env.VITE_API_BASE_URL + `/file/chunk/list/${fileId}`,
    })
  );
  return data;
};

/**
 * 删除上传的文件
 * @param {string} fileId - 文件ID
 * @returns {Promise}
 */
export const deleteUploadedFile = async (fileId) => {
  const { data } = await axios(
    createRequestConfig({
      method: "delete",
      url: import.meta.env.VITE_API_BASE_URL + `/file/${fileId}`,
    })
  );
  return data;
};
