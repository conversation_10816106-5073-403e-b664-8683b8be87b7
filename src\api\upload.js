import axios from "axios";
import { useAuthStore } from "@/stores/auth";

// 表单形式上传图片
export const postUploadFile = async (file) => {
  const formData = new FormData();
  formData.append("file", file);
  const authStore = useAuthStore();
  const token = "";
  if (authStore.getToken && authStore.getToken != "") {
    token = authStore.getToken;
  }
  const { data } = await axios({
    method: "post",
    timeout: 10000,
    url: import.meta.env.VITE_API_BASE_URL + "/file/common/image/upload",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
      token,
    },
  });
  return data;
};
