// 导入Vue的createApp函数
import { createApp } from "vue";
// 导入Element Plus
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import "element-plus/theme-chalk/dark/css-vars.css";
import "./styles/element-plus-theme.css";
import "./styles/dark/css-vars.css";

// 导入Element Plus图标
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
// 导入全局样式
import "./style.css";
// 导入根组件
import App from "./App.vue";
// 导入路由配置
import router from "./router";
// 导入Pinia状态管理
import pinia from "./stores";

// 创建Vue应用实例
const app = createApp(App);

// 使用Pinia状态管理
app.use(pinia);

// 使用Element Plus
app.use(ElementPlus);

// 注册所有Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

// 使用路由插件
app.use(router);

// 挂载应用到DOM
app.mount("#app");
