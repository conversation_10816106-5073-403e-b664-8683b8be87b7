<template>
  <!-- 面包屑导航组件 -->
  <div class="breadcrumb-container">
    <el-breadcrumb separator=">" class="app-breadcrumb">
      <el-breadcrumb-item @click="goHome" class="clickable">
        <div class="home-link">
          <el-icon>
            <House />
          </el-icon>
          <span class="text">Home</span>
        </div>
      </el-breadcrumb-item>

      <!-- 动态生成面包屑路径 -->
      <el-breadcrumb-item v-for="(breadcrumb, index) in breadcrumbs" :key="breadcrumb.path" :class="{
        clickable: index < breadcrumbs.length - 1,
        current: index === breadcrumbs.length - 1
      }" @click="index < breadcrumbs.length - 1 ? goToPath(breadcrumb.path) : null">
        {{ breadcrumb.title }}
      </el-breadcrumb-item>
    </el-breadcrumb>
  </div>
</template>

<script setup>
// 导入Vue组合式API
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 获取路由实例
const route = useRoute()
const router = useRouter()

// 生成面包屑路径
const breadcrumbs = computed(() => {
  // 获取所有匹配的路由，只要有 meta.menu.title 就显示
  const matched = route.matched.filter(item =>
    item.meta &&
    item.meta.menu &&
    item.meta.menu.title &&
    item.path !== '/' // 排除根路径
  )

  return matched.map((item, index) => {
    let path = item.path

    // 构建完整路径
    if (index > 0 && !path.startsWith('/')) {
      // 对于子路由，需要构建完整路径
      const parentPath = matched[index - 1].path
      path = `${parentPath}/${path}`.replace(/\/+/g, '/') // 去除多余的斜杠
    }

    return {
      path: path,
      title: item.meta.menu.title
    }
  })
})

// 返回首页
const goHome = () => {
  if (route.path !== '/weDesign') {
    router.push('/weDesign').catch(err => {
      console.log('导航到首页失败:', err)
    })
  }
}

// 跳转到指定路径
const goToPath = (path) => {
  if (route.path !== path) {
    router.push(path).catch(err => {
      console.log('导航失败:', err)
    })
  }
}
</script>

<style lang="scss" scoped>
.breadcrumb-container {
  position: sticky;
  top: 0px;
  z-index: 100;
  background-color: #201F2F;
  padding: 20px 0;
}

.home-link {
  display: flex;
  align-items: center;

  .text {
    margin-left: 4px;
  }
}

.app-breadcrumb {
  display: flex;
  align-items: center;
}

.app-breadcrumb :deep(.el-breadcrumb__item) {
  color: #FFF;
  font-size: 16px;
}

.app-breadcrumb :deep(.el-breadcrumb__item:last-child) {
  color: #FFF;
  font-weight: 500;
}

.app-breadcrumb :deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #FFF;
  font-weight: 500;
}

// 可点击的面包屑项
.app-breadcrumb :deep(.el-breadcrumb__item.clickable .el-breadcrumb__inner) {
  cursor: pointer;
  transition: color 0.3s ease;
}

.app-breadcrumb :deep(.el-breadcrumb__item.clickable .el-breadcrumb__inner:hover) {
  color: #E34234;
}

// 当前页面的面包屑项（不可点击）
.app-breadcrumb :deep(.el-breadcrumb__item.current .el-breadcrumb__inner) {
  cursor: default;
  opacity: 0.8;
}

// 所有面包屑项的基础样式
.app-breadcrumb :deep(.el-breadcrumb__item .el-breadcrumb__inner) {
  transition: color 0.3s ease;
}
</style>
