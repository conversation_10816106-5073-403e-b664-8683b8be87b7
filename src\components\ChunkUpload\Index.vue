<template>
  <div class="chunk-upload">
    <div class="upload-area" @click="triggerFileInput" @drop="handleDrop" @dragover.prevent @dragenter.prevent>
      <input 
        ref="fileInputRef" 
        type="file" 
        :accept="acceptTypes" 
        @change="handleFileSelect" 
        style="display: none"
      />
      
      <div v-if="!uploadState.file" class="upload-placeholder">
        <slot name="placeholder">
          <div class="default-placeholder">
            <el-icon size="48" color="#999">
              <Upload />
            </el-icon>
            <p>点击或拖拽文件到此处上传</p>
            <p class="file-types">支持 {{ fileTypeText }}</p>
          </div>
        </slot>
      </div>

      <div v-else class="upload-progress">
        <div class="file-info">
          <el-icon><Document /></el-icon>
          <span class="file-name">{{ uploadState.file.name }}</span>
          <span class="file-size">({{ formatFileSize(uploadState.file.size) }})</span>
        </div>
        
        <div class="progress-section">
          <el-progress 
            :percentage="uploadState.progress" 
            :status="uploadState.status === 'error' ? 'exception' : uploadState.status === 'success' ? 'success' : ''"
            :stroke-width="8"
          />
          <div class="progress-info">
            <span>{{ uploadState.statusText }}</span>
            <span v-if="uploadState.status === 'uploading'">
              {{ uploadState.uploadedChunks }}/{{ uploadState.totalChunks }} 分片
            </span>
          </div>
        </div>

        <div class="upload-actions">
          <el-button v-if="uploadState.status === 'ready'" type="primary" @click="startUpload">
            开始上传
          </el-button>
          <el-button v-if="uploadState.status === 'uploading'" @click="pauseUpload">
            暂停
          </el-button>
          <el-button v-if="uploadState.status === 'paused'" type="primary" @click="resumeUpload">
            继续
          </el-button>
          <el-button v-if="uploadState.status === 'error'" type="primary" @click="retryUpload">
            重试
          </el-button>
          <el-button @click="cancelUpload">取消</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElProgress, ElButton, ElIcon } from 'element-plus'
import { Upload, Document } from '@element-plus/icons-vue'
import { uploadChunk, mergeChunks } from '@/api/upload'

// Props 定义
const props = defineProps({
  // 接受的文件类型
  accept: {
    type: String,
    default: 'image/*,video/*,.ppt,.pptx,.pdf,.doc,.docx'
  },
  // 分片大小 (默认 2MB)
  chunkSize: {
    type: Number,
    default: 2 * 1024 * 1024
  },
  // 最大文件大小 (默认 100MB)
  maxSize: {
    type: Number,
    default: 100 * 1024 * 1024
  },
  // 并发上传数量
  concurrent: {
    type: Number,
    default: 3
  }
})

// Emits 定义
const emit = defineEmits(['upload-success', 'upload-error', 'upload-progress'])

// 响应式数据
const fileInputRef = ref()
const uploadState = reactive({
  file: null,           // 当前文件
  fileId: null,         // 文件ID
  chunks: [],           // 分片数组
  totalChunks: 0,       // 总分片数
  uploadedChunks: 0,    // 已上传分片数
  progress: 0,          // 上传进度
  status: 'ready',      // 状态: ready, uploading, paused, success, error
  statusText: '准备上传', // 状态文本
  uploadingQueue: [],   // 正在上传的分片队列
  failedChunks: []      // 失败的分片
})

// 计算属性
const acceptTypes = computed(() => props.accept)
const fileTypeText = computed(() => {
  const types = props.accept.split(',').map(type => type.trim())
  return types.join(', ')
})

// 触发文件选择
const triggerFileInput = () => {
  if (uploadState.status === 'uploading') return
  fileInputRef.value?.click()
}

// 处理文件选择
const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    handleFile(file)
  }
}

// 处理拖拽上传
const handleDrop = (event) => {
  event.preventDefault()
  if (uploadState.status === 'uploading') return
  
  const files = event.dataTransfer.files
  if (files.length > 0) {
    handleFile(files[0])
  }
}

// 处理文件
const handleFile = (file) => {
  // 文件大小验证
  if (file.size > props.maxSize) {
    ElMessage.error(`文件大小不能超过 ${formatFileSize(props.maxSize)}`)
    return
  }

  // 重置状态
  resetUploadState()
  
  // 设置文件信息
  uploadState.file = file
  uploadState.fileId = generateFileId()
  
  // 创建分片
  createChunks(file)
  
  updateStatus('ready', '准备上传')
}

// 生成文件ID
const generateFileId = () => {
  return Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 创建分片
const createChunks = (file) => {
  const chunks = []
  const totalChunks = Math.ceil(file.size / props.chunkSize)
  
  for (let i = 0; i < totalChunks; i++) {
    const start = i * props.chunkSize
    const end = Math.min(start + props.chunkSize, file.size)
    const chunk = file.slice(start, end)
    
    chunks.push({
      chunkNumber: i,
      chunk: chunk,
      uploaded: false,
      uploading: false,
      error: false
    })
  }
  
  uploadState.chunks = chunks
  uploadState.totalChunks = totalChunks
}

// 开始上传
const startUpload = async () => {
  updateStatus('uploading', '上传中...')
  
  try {
    await uploadChunksWithConcurrency()
    await mergeFile()
    
    updateStatus('success', '上传成功')
    emit('upload-success', {
      fileId: uploadState.fileId,
      fileName: uploadState.file.name,
      fileSize: uploadState.file.size
    })
  } catch (error) {
    updateStatus('error', '上传失败')
    emit('upload-error', error)
  }
}

// 并发上传分片
const uploadChunksWithConcurrency = async () => {
  const pendingChunks = uploadState.chunks.filter(chunk => !chunk.uploaded && !chunk.error)
  
  while (pendingChunks.length > 0 && uploadState.status === 'uploading') {
    const currentBatch = pendingChunks.splice(0, props.concurrent)
    const promises = currentBatch.map(chunk => uploadSingleChunk(chunk))
    
    await Promise.allSettled(promises)
    updateProgress()
  }
}

// 上传单个分片
const uploadSingleChunk = async (chunkInfo) => {
  if (chunkInfo.uploaded || uploadState.status !== 'uploading') return
  
  chunkInfo.uploading = true
  
  try {
    const formData = new FormData()
    formData.append('fileId', uploadState.fileId)
    formData.append('chunkNumber', chunkInfo.chunkNumber)
    formData.append('totalChunks', uploadState.totalChunks)
    formData.append('chunk', chunkInfo.chunk)
    
    await uploadChunk(formData)
    
    chunkInfo.uploaded = true
    chunkInfo.uploading = false
    uploadState.uploadedChunks++
    
  } catch (error) {
    chunkInfo.error = true
    chunkInfo.uploading = false
    uploadState.failedChunks.push(chunkInfo)
    throw error
  }
}

// 合并文件
const mergeFile = async () => {
  updateStatus('uploading', '合并文件中...')
  
  try {
    await mergeChunks({
      fileId: uploadState.fileId,
      fileName: uploadState.file.name,
      totalChunks: uploadState.totalChunks
    })
  } catch (error) {
    throw new Error('文件合并失败')
  }
}

// 暂停上传
const pauseUpload = () => {
  updateStatus('paused', '已暂停')
}

// 继续上传
const resumeUpload = () => {
  startUpload()
}

// 重试上传
const retryUpload = () => {
  // 重置失败的分片
  uploadState.failedChunks.forEach(chunk => {
    chunk.error = false
    chunk.uploading = false
  })
  uploadState.failedChunks = []
  
  startUpload()
}

// 取消上传
const cancelUpload = () => {
  resetUploadState()
}

// 更新进度
const updateProgress = () => {
  const progress = Math.round((uploadState.uploadedChunks / uploadState.totalChunks) * 100)
  uploadState.progress = progress
  
  emit('upload-progress', {
    progress,
    uploadedChunks: uploadState.uploadedChunks,
    totalChunks: uploadState.totalChunks
  })
}

// 更新状态
const updateStatus = (status, statusText) => {
  uploadState.status = status
  uploadState.statusText = statusText
}

// 重置上传状态
const resetUploadState = () => {
  uploadState.file = null
  uploadState.fileId = null
  uploadState.chunks = []
  uploadState.totalChunks = 0
  uploadState.uploadedChunks = 0
  uploadState.progress = 0
  uploadState.status = 'ready'
  uploadState.statusText = '准备上传'
  uploadState.uploadingQueue = []
  uploadState.failedChunks = []
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style lang="scss" scoped>
.chunk-upload {
  .upload-area {
    border: 2px dashed #dcdfe6;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: border-color 0.3s;

    &:hover {
      border-color: #409eff;
    }

    .upload-placeholder {
      .default-placeholder {
        p {
          margin: 10px 0;
          color: #606266;
        }

        .file-types {
          font-size: 12px;
          color: #909399;
        }
      }
    }

    .upload-progress {
      text-align: left;

      .file-info {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 15px;

        .file-name {
          font-weight: 500;
        }

        .file-size {
          color: #909399;
          font-size: 12px;
        }
      }

      .progress-section {
        margin-bottom: 15px;

        .progress-info {
          display: flex;
          justify-content: space-between;
          margin-top: 8px;
          font-size: 12px;
          color: #606266;
        }
      }

      .upload-actions {
        display: flex;
        gap: 10px;
      }
    }
  }
}
</style>
