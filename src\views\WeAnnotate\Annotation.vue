<template>
  <div class="annotation-page">
    <div class="filters">
      <div class="filter-group" v-for="filter in filterOptions" :key="filter.key">
        <el-dropdown @command="(value) => handleFilter<PERSON>hange(filter.key, value)" class="filter-dropdown">
          <span class="filter-button">
            {{ getFilter<PERSON>abel(filter) }}
            <el-icon class="dropdown-icon">
              <ArrowDown />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="option in filter.options" :key="option.value" :command="option.value"
                :class="{ active: filters[filter.key] === option.value }">
                {{ option.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <div class="content-grid">
      <div class="content-card" v-for="item in contentItems" :key="item.id" @click="handleCardClick(item.id)">
        <div class="card-image" :style="{ backgroundImage: `url(${item.image})` }">
          <div class="card-overlay">
            <h3 class="card-title">{{ item.title }}</h3>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'
import logo1 from '@/assets/1.jpg';
import logo2 from '@/assets/2.jpg';
import logo3 from '@/assets/3.jpg';
import logo4 from '@/assets/4.jpg';
import logo5 from '@/assets/5.jpg';
import logo6 from '@/assets/6.jpg';
import logo7 from '@/assets/7.jpg';
import logo8 from '@/assets/8.jpg';
import logo9 from '@/assets/9.png';
// 筛选器数据
const filters = reactive({
  discipline: '',
  contentFormat: '',
  uploadTime: '',
  hostLanguage: ''
})

// 筛选器选项配置
const filterOptions = ref([
  {
    key: 'discipline',
    placeholder: 'Discipline',
    options: [
      { label: 'All Disciplines', value: '' },
      { label: 'Computer Science', value: 'cs' },
      { label: 'Mathematics', value: 'math' },
      { label: 'Physics', value: 'physics' },
      { label: 'Data Science', value: 'data-science' }
    ]
  },
  {
    key: 'contentFormat',
    placeholder: 'Content Format',
    options: [
      { label: 'All Formats', value: '' },
      { label: 'Video', value: 'video' },
      { label: 'Article', value: 'article' },
      { label: 'Tutorial', value: 'tutorial' },
      { label: 'Course', value: 'course' }
    ]
  },
  {
    key: 'uploadTime',
    placeholder: 'Upload Time',
    options: [
      { label: 'All Time', value: '' },
      { label: 'Last Week', value: 'week' },
      { label: 'Last Month', value: 'month' },
      { label: 'Last Year', value: 'year' }
    ]
  },
  {
    key: 'hostLanguage',
    placeholder: 'Host Language',
    options: [
      { label: 'All Languages', value: '' },
      { label: 'English', value: 'en' },
      { label: 'Chinese', value: 'zh' },
      { label: 'Spanish', value: 'es' }
    ]
  }
])

// 内容项目数据
const contentItems = ref([
  {
    id: 1,
    title: 'Decision Tree-Based Machine Learning Algorithms and Their Applications in Classification',
    image: logo1
  },
  {
    id: 2,
    title: 'Implementing Token-Based Authentication for Secure Web Applications',
    image: logo2
  },
  {
    id: 3,
    title: 'Smart Contracts 101: Writing and Testing with Solidity',
    image: logo3
  },
  {
    id: 4,
    title: 'The Role of APIs in Building Seamless Data Integration',
    image: logo4
  },
  {
    id: 5,
    title: 'Optimizing Neural Networks: Activation Functions and Dropout Techniques',
    image: logo5
  },
  {
    id: 6,
    title: 'Intro to Containers: Understanding Docker and Kubernetes Basics',
    image: logo6
  },
  {
    id: 7,
    title: 'Deep Dive into Cybersecurity: Encryption Algorithms and Key Management',
    image: logo7
  },
  {
    id: 8,
    title: 'NFT Metadata Standards and Minting Best Practices',
    image: logo8
  },
  {
    id: 9,
    title: 'Building Microservices Architecture with Node.js',
    image: logo9
  },
  {
    id: 10,
    title: 'Exploring Graph Databases: Practical Applications of Neo4j',
    image: logo1
  },
  {
    id: 11,
    title: 'Developing Decentralized Identity Systems with DID Protocols',
    image: logo2
  },
  {
    id: 12,
    title: 'Edge Computing: Deploying AI Models on IoT Devices',
    image: logo3
  }
])

// 处理筛选器变化
const handleFilterChange = (filterKey, value) => {
  filters[filterKey] = value
  console.log('筛选器变化:', filterKey, value)
}

// 获取筛选器显示标签
const getFilterLabel = (filter) => {
  const currentValue = filters[filter.key]
  if (!currentValue) {
    return filter.placeholder
  }
  const selectedOption = filter.options.find(option => option.value === currentValue)
  return selectedOption ? selectedOption.label : filter.placeholder
}

// 处理卡片点击
const handleCardClick = (id) => {
  console.log('点击卡片:', id)
  // 这里可以添加跳转到详情页的逻辑
}
</script>

<style lang="scss" scoped>
.annotation-page {
  padding: 20px 20px 0 0;
  margin-right: 20px;
  margin-bottom: 20px;

  // 筛选器样式
  .filters {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    flex-wrap: wrap;

    .filter-group {
      .filter-dropdown {
        .filter-button {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 16px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 6px;
          color: #fff;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s ease;
          min-width: 140px;
          justify-content: space-between;

          &:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
          }

          .dropdown-icon {
            font-size: 12px;
            transition: transform 0.3s ease;
          }
        }
      }
    }
  }

  // 内容网格样式
  .content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(375px, 1fr));
    gap: 20px;

    .content-card {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid rgba(255, 255, 255, 0.1);

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 255, 255, 0.2);
      }

      .card-image {
        width: 100%;
        height: 260px;
        border-radius: 10px;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        position: relative;
        background-color: #2a2a2a;

        .card-overlay {
          position: absolute;
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          background: rgba(0, 0, 0, 0.4);
          padding: 20px 16px 16px;

          .card-title {
            color: #fff;
            font-size: 16px;
            font-weight: 600;
            margin: 0;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }
      }
    }
  }
}

// Element Plus 下拉菜单样式覆盖
:deep(.el-dropdown-menu) {
  background: rgba(30, 30, 30, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);

  .el-dropdown-menu__item {
    color: #fff;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: #fff;
    }

    &.active {
      background: rgba(227, 66, 52, 0.2);
      color: #E34234;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .annotation-page {
    padding: 16px;

    .filters {
      gap: 12px;

      .filter-group {
        .filter-dropdown {
          .filter-button {
            min-width: 120px;
            padding: 6px 12px;
            font-size: 13px;
          }
        }
      }
    }

    .content-grid {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 16px;
    }
  }
}

@media (max-width: 480px) {
  .annotation-page {
    .filters {
      flex-direction: column;

      .filter-group {
        .filter-dropdown {
          .filter-button {
            width: 100%;
          }
        }
      }
    }

    .content-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
