# Token解析错误修复文档

## 问题描述

在布局重构后，应用出现了以下错误：
```
解析token失败: InvalidCharacterError: Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded.
```

## 问题原因

1. **Mock Token格式问题**：之前使用的mock token格式为 `mock-jwt-token-{timestamp}`，这不是有效的JWT格式
2. **JWT解析失败**：`checkTokenExpiry` 函数尝试使用 `atob()` 解析非JWT格式的token时失败
3. **缺少格式验证**：没有在解析前验证token是否为有效的JWT格式

## 解决方案

### 1. 创建Mock JWT生成器

添加了 `generateMockJWT` 函数来生成有效的JWT格式token：

```javascript
const generateMockJWT = (payload = {}) => {
  const header = {
    alg: "HS256",
    typ: "JWT"
  };
  
  const defaultPayload = {
    sub: "1234567890",
    name: payload.username || "Mock User",
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24小时后过期
    ...payload
  };
  
  // 简单的base64编码（仅用于mock）
  const encodedHeader = btoa(JSON.stringify(header));
  const encodedPayload = btoa(JSON.stringify(defaultPayload));
  const signature = "mock-signature";
  
  return `${encodedHeader}.${encodedPayload}.${signature}`;
};
```

### 2. 更新登录方法

修改登录方法使用新的JWT生成器：

```javascript
// 模拟登录响应，使用有效的JWT格式
response = {
  token: generateMockJWT({
    username: loginData.username,
    email: loginData.email || `${loginData.username}@example.com`,
    roles: ["user"]
  }),
  userInfo: {
    // ... 用户信息
  }
};
```

### 3. 更新Token刷新方法

修改refreshToken方法使用新的JWT生成器：

```javascript
// 模拟刷新token响应，使用有效的JWT格式
response = {
  token: generateMockJWT({
    username: userInfo.value?.username || "User",
    email: userInfo.value?.email || "<EMAIL>",
    roles: userInfo.value?.roles || ["user"]
  })
};
```

### 4. 改进Token过期检查

优化 `checkTokenExpiry` 函数，添加更好的错误处理：

```javascript
const checkTokenExpiry = () => {
  if (!token.value) return false;

  try {
    // 检查token格式是否为JWT (应该有3个部分，用.分隔)
    const tokenParts = token.value.split(".");
    if (tokenParts.length !== 3) {
      console.warn("Token格式不正确，不是有效的JWT");
      return true; // 格式不正确认为已过期
    }

    // 解析JWT token
    const payload = JSON.parse(atob(tokenParts[1]));
    const currentTime = Date.now() / 1000;

    // 如果token即将在5分钟内过期，返回true
    return payload.exp && payload.exp - currentTime < 300;
  } catch (error) {
    console.error("解析token失败:", error);
    return true; // 解析失败认为已过期
  }
};
```

## JWT Token结构

生成的Mock JWT Token包含以下信息：

### Header
```json
{
  "alg": "HS256",
  "typ": "JWT"
}
```

### Payload
```json
{
  "sub": "1234567890",
  "name": "用户名",
  "iat": 1640995200,
  "exp": 1641081600,
  "username": "用户名",
  "email": "用户邮箱",
  "roles": ["user"]
}
```

### Signature
```
mock-signature (在生产环境中应该是真实的HMAC签名)
```

## 技术优势

### 1. 兼容性
- Mock token现在与真实JWT token格式兼容
- 可以无缝切换到真实的JWT token
- 支持标准的JWT解析和验证

### 2. 功能完整性
- 支持token过期检查
- 包含完整的用户信息
- 支持角色和权限管理

### 3. 开发体验
- 更真实的开发环境
- 更好的错误处理
- 清晰的日志输出

## 测试验证

### 1. Token生成测试
- ✅ 登录时生成有效的JWT格式token
- ✅ Token刷新时生成新的有效token
- ✅ Token包含正确的用户信息和过期时间

### 2. Token解析测试
- ✅ 可以正确解析token payload
- ✅ 可以正确检查token过期时间
- ✅ 错误token格式时正确处理

### 3. 认证流程测试
- ✅ 登录流程正常工作
- ✅ Token过期检查正常工作
- ✅ 自动刷新token功能正常

## 生产环境注意事项

1. **使用真实JWT库**：生产环境应使用如 `jsonwebtoken` 等专业库
2. **安全签名**：使用真实的密钥进行HMAC签名
3. **Token验证**：实现完整的token验证逻辑
4. **过期策略**：根据业务需求设置合适的过期时间

## 总结

通过生成有效的JWT格式mock token，我们解决了token解析错误问题，同时保持了与真实JWT token的兼容性。这个修复不仅解决了当前的错误，还为将来切换到真实的JWT认证系统奠定了基础。
