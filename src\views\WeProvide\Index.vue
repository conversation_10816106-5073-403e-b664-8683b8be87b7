<template>
  <div>
    <!-- 如果有子路由，显示子路由内容 -->
    <router-view v-if="$route.matched.length > 1" />
    <!-- 如果没有子路由，显示默认内容 -->
    <div class="weprovide-container" v-else>
      <div class="cerate-course">
        <div class="left-item">
          <img class="icon" src="@/assets/header-logo.png" alt="logo">
          <p class="introduce">All-In-One Intelligent Instructor Support System</p>
          <div class="button" @click="handleCreate">
            <img class="icon" src="@/assets/add-weprovide.png" alt="icon">
            <span class="text">Create</span>
          </div>
        </div>
        <img class="right-item" src="@/assets/workbench.png" />
      </div>
    </div>
  </div>
</template>

<script setup>

import { useRouter } from 'vue-router'

const router = useRouter()

// 创建
const handleCreate = () => {
  router.push('/weProvide/create')
}
</script>

<style lang="scss" scoped>
.weprovide-container {
  width: 100%;

  .cerate-course {
    width: 100%;
    margin-top: 10%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 147px 0 63px;
    box-sizing: border-box;

    .left-item {
      display: flex;
      flex-direction: column;

      .icon {
        width: 236px;
        height: 28px;
      }

      .introduce {
        font-weight: 500;
        font-size: 30px;
        color: #FEFEFE;
        margin: 15px 0 30px;
      }

      .button {
        width: 209px;
        height: 70px;
        background: rgba(79, 39, 48, 0.63);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        display: flex;
        cursor: pointer;
        transition: all 0.3s ease;

        .icon {
          width: 37px;
          height: 37px;
        }

        .text {
          font-weight: 400;
          font-size: 25px;
          color: #E34234;
          margin-left: 20px;
          user-select: none;
        }

        &:hover {
          background: rgba(227, 66, 52, 0.3);
        }
      }
    }

    .right-item {
      width: 441px;
      height: 361px;
      margin-top: 33px;
    }
  }
}
</style>
